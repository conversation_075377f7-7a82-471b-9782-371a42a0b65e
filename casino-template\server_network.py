#!/usr/bin/env python3
"""
Network-optimized Python HTTP Server for Casino Template
Binds to all interfaces for better network accessibility
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import socket
from pathlib import Path

class CasinoHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for the casino template"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Add cache control for development
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        super().end_headers()
    
    def do_GET(self):
        # Serve index.html for root path
        if self.path == '/':
            self.path = '/index.html'
        
        # Handle file extensions for proper MIME types
        if self.path.endswith('.js'):
            self.send_response(200)
            self.send_header('Content-type', 'application/javascript')
            self.end_headers()
            try:
                with open(self.path[1:], 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404)
            return
        
        if self.path.endswith('.css'):
            self.send_response(200)
            self.send_header('Content-type', 'text/css')
            self.end_headers()
            try:
                with open(self.path[1:], 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404)
            return
        
        # Default handling for other files
        super().do_GET()
    
    def log_message(self, format, *args):
        """Custom log message format"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"

def get_all_ips():
    """Get all available IP addresses"""
    hostname = socket.gethostname()
    ips = []
    
    try:
        # Get all IP addresses for this host
        addresses = socket.getaddrinfo(hostname, None)
        ip_set = set()
        for addr in addresses:
            ip = addr[4][0]
            if not ip.startswith('::') and ip != '127.0.0.1':  # Skip IPv6 and localhost
                ip_set.add(ip)
        ips = list(ip_set)
    except Exception:
        pass
    
    # Add the detected IP if not already in list
    detected_ip = get_local_ip()
    if detected_ip not in ips and detected_ip != "127.0.0.1":
        ips.append(detected_ip)
    
    return sorted(ips)

def find_free_port(start_port=8000, max_attempts=100):
    """Find a free port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))  # Bind to all interfaces
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def main():
    """Main server function"""
    # Change to the directory containing this script
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)
    
    # Check if index.html exists
    if not os.path.exists('index.html'):
        print("❌ Error: index.html not found in current directory!")
        print(f"Current directory: {os.getcwd()}")
        print("Make sure you're running this from the casino-template directory.")
        sys.exit(1)
    
    # Get all available IPs
    all_ips = get_all_ips()
    primary_ip = get_local_ip()
    
    # Find a free port
    try:
        port = find_free_port()
    except RuntimeError as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    
    # Create and start the server - bind to all interfaces
    try:
        with socketserver.TCPServer(("", port), CasinoHTTPRequestHandler) as httpd:
            localhost_url = f"http://localhost:{port}"
            
            print("🎰 Casino Template Network Server Starting...")
            print("=" * 70)
            print(f"🏠 Local URL:      {localhost_url}")
            print(f"🚀 Port:           {port}")
            print(f"📁 Serving from:   {os.getcwd()}")
            print("=" * 70)
            print("🌐 Network URLs (accessible from other devices):")
            
            for ip in all_ips:
                network_url = f"http://{ip}:{port}"
                print(f"   📱 {network_url}")
            
            print("=" * 70)
            print("💡 Troubleshooting:")
            print("   • If other devices can't connect, check Windows Firewall")
            print("   • Run 'python network_test.py' for diagnostics")
            print("   • Try running as Administrator")
            print("=" * 70)
            print("📝 Server Logs:")
            print("Press Ctrl+C to stop the server")
            print()
            
            # Try to open browser automatically
            try:
                webbrowser.open(localhost_url)
                print(f"🌐 Opened {localhost_url} in your default browser")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"Please manually open: {localhost_url}")
            
            print()
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
