/* RocketBet Casino Template - Professional Styling */

:root {
    --primary-color: #ffc107;
    --primary-dark: #e0a800;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --bg-dark: #0a0a0a;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --border-color: rgba(255, 193, 7, 0.3);
    --glow-color: rgba(255, 193, 7, 0.5);
    --gradient-primary: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--gradient-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    line-height: 1.2;
}

/* Navigation */
.navbar {
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.98) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);
}

.navbar-brand {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.navbar-brand:hover {
    color: var(--primary-dark) !important;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(255, 193, 7, 0.1);
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--primary-color);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-warning {
    background: var(--gradient-primary);
    color: #000;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    color: #000;
}

.btn-outline-warning {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-warning:hover {
    background: var(--primary-color);
    color: #000;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 193, 7, 0.1) 0%, transparent 70%);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #fff, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 193, 7, 0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 193, 7, 0.6), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
}

@keyframes sparkle {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    margin-bottom: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(255, 193, 7, 0.3));
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 500px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.feature-item i {
    font-size: 1.2rem;
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Hero Animation */
.rocket-animation {
    position: relative;
    text-align: center;
    margin-bottom: 3rem;
}

.rocket-animation i {
    font-size: 8rem;
    color: var(--primary-color);
    filter: drop-shadow(0 0 30px var(--glow-color));
    animation: rocketFloat 4s ease-in-out infinite;
}

@keyframes rocketFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(5deg); }
    50% { transform: translateY(-10px) rotate(0deg); }
    75% { transform: translateY(-30px) rotate(-5deg); }
}

.stats-cards {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    min-width: 120px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2);
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Section Styling */
.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

/* Games Section */
.games-section {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.game-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(255, 193, 7, 0.2);
    border-color: var(--primary-color);
}

.game-card.featured {
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
}

.game-image {
    position: relative;
    height: 200px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.game-image i {
    font-size: 4rem;
    color: #000;
    transition: all 0.3s ease;
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.game-card:hover .game-overlay {
    opacity: 1;
}

.game-card:hover .game-image i {
    transform: scale(1.2);
}

.game-info {
    padding: 1.5rem;
}

.game-info h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.game-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.game-stats {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Rocket Game Section */
.rocket-game-section {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(0, 0, 0, 0.3) 100%);
}

/* Modern Game Layout */
.modern-game-layout {
    display: grid;
    grid-template-columns: 280px 1fr 120px;
    gap: 0;
    height: 700px;
    background: linear-gradient(135deg, #1a1f3a 0%, #2d3561 50%, #1a1f3a 100%);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* Left Sidebar */
.game-left-sidebar {
    background: rgba(0, 0, 0, 0.4);
    padding: 20px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.betting-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.bet-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

.stat-value {
    display: flex;
    align-items: center;
    gap: 6px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #ffc107;
    font-size: 14px;
}

.stat-value i {
    font-size: 12px;
}

.bet-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.bet-input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0 12px;
    height: 40px;
}

.bet-input-container i,
.input-prefix,
.input-suffix {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    font-weight: 600;
}

.bet-input-container input {
    background: transparent;
    border: none;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 16px;
    width: 100%;
    padding: 0 8px;
    outline: none;
}

.bet-input-container:focus-within {
    border-color: #4285f4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.bet-quick-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
    margin-top: 8px;
}

.quick-bet-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    font-weight: 600;
    padding: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-bet-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.auto-bet-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toggle-switch {
    width: 40px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    position: relative;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-switch:checked {
    background: #4285f4;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: white;
    top: 2px;
    left: 2px;
    transition: all 0.3s ease;
}

.toggle-switch:checked::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    cursor: pointer;
}

.main-bet-button,
.main-cashout-button {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
}

.main-bet-button {
    background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);
}

.main-bet-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
}

.main-cashout-button {
    background: linear-gradient(135deg, #00c851 0%, #00a63f 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 200, 81, 0.3);
    animation: cashOutPulse 1.5s ease-in-out infinite;
}

.main-cashout-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 200, 81, 0.4);
}

@keyframes cashOutPulse {
    0%, 100% { box-shadow: 0 4px 15px rgba(0, 200, 81, 0.3); }
    50% { box-shadow: 0 6px 25px rgba(0, 200, 81, 0.6); }
}

.btn-text {
    font-size: 16px;
}

.btn-amount {
    font-size: 14px;
    opacity: 0.9;
}

/* Players Section */
.players-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.players-header {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.players-header span {
    color: #ffc107;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
}

.players-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.player-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.player-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4, #1976d2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 700;
    color: white;
}

.player-name {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.player-amount {
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    font-weight: 700;
    color: #ffc107;
}

.player-item.cashed-out {
    opacity: 0.7;
    background: rgba(0, 200, 81, 0.1);
    border-radius: 4px;
    padding: 4px;
}

.player-item.cashed-out .player-amount {
    color: #00c851;
}

.cash-out-amount {
    font-weight: 700;
    color: #00c851 !important;
}

.player-item.current-user {
    background: rgba(66, 133, 244, 0.1);
    border: 1px solid rgba(66, 133, 244, 0.3);
    border-radius: 6px;
    padding: 8px;
}

.current-user-avatar {
    background: linear-gradient(135deg, #4285f4, #1976d2) !important;
    color: white !important;
    font-weight: 700;
}

.player-item.current-user .player-name {
    color: #4285f4;
    font-weight: 700;
}

.no-players {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    padding: 20px 10px;
    font-style: italic;
}

/* Main Game Area */
.game-main-area {
    display: flex;
    flex-direction: column;
    position: relative;
    background: linear-gradient(135deg, #1e2a5e 0%, #2d3561 50%, #1e2a5e 100%);
}

.multiplier-history {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item {
    padding: 6px 12px;
    border-radius: 20px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 12px;
    color: white;
    min-width: 50px;
    text-align: center;
}

.history-item.crashed {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.history-item.low {
    background: linear-gradient(135deg, #ffa502, #ff6348);
}

.history-item.medium {
    background: linear-gradient(135deg, #3742fa, #2f3542);
}

.history-item.high {
    background: linear-gradient(135deg, #2ed573, #1e90ff);
}

.history-item.very-high {
    background: linear-gradient(135deg, #ffc107, #ff8f00);
}

.modern-game-canvas-container {
    position: relative;
    flex: 1;
    overflow: hidden;
}

#rocketCanvas {
    width: 100%;
    height: 100%;
    display: block;
}

.central-multiplier {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.multiplier-value {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    color: white;
    text-shadow:
        0 0 20px rgba(255, 255, 255, 0.8),
        0 0 40px rgba(66, 133, 244, 0.6),
        0 0 60px rgba(66, 133, 244, 0.4);
    animation: multiplierPulse 2s ease-in-out infinite;
    line-height: 1;
}

.multiplier-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    margin-top: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes multiplierPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

.game-status-modern {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.time-scale {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    height: 20px;
}

.time-markers {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    position: relative;
}

.time-markers::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1;
}

.time-markers span {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 600;
    background: rgba(0, 0, 0, 0.5);
    padding: 2px 6px;
    border-radius: 4px;
    z-index: 2;
    position: relative;
}

/* Right Sidebar */
.game-right-sidebar {
    background: rgba(0, 0, 0, 0.4);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 10px;
    display: flex;
    flex-direction: column;
}

.probability-scale {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
    justify-content: space-between;
}

.scale-label {
    font-family: 'Orbitron', monospace;
    font-size: 11px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    margin-bottom: 10px;
}

.scale-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.scale-item.active {
    background: rgba(66, 133, 244, 0.2);
    border: 1px solid rgba(66, 133, 244, 0.4);
}

.scale-multiplier {
    font-family: 'Orbitron', monospace;
    font-size: 10px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.8);
}

.scale-item.active .scale-multiplier {
    color: #4285f4;
}

/* Responsive Design for Modern Layout */
@media (max-width: 1200px) {
    .modern-game-layout {
        grid-template-columns: 250px 1fr 100px;
        height: 600px;
    }

    .multiplier-value {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .modern-game-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        height: auto;
        min-height: 500px;
    }

    .game-left-sidebar {
        order: 2;
        border-right: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        flex-direction: row;
        gap: 20px;
        overflow-x: auto;
    }

    .game-right-sidebar {
        display: none;
    }

    .betting-section {
        min-width: 200px;
    }

    .players-section {
        min-width: 150px;
    }

    .multiplier-value {
        font-size: 2.5rem;
    }
}

.login-prompt-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 3rem;
    max-width: 600px;
    margin: 0 auto;
}

.rocket-icon i {
    font-size: 4rem;
    color: var(--primary-color);
    filter: drop-shadow(0 0 20px var(--glow-color));
    animation: rocketFloat 3s ease-in-out infinite;
}

.prompt-features {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
    flex-wrap: wrap;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
}

.feature i {
    font-size: 1.5rem;
}

/* Game Canvas */
.game-canvas-container {
    position: relative;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 20px;
    border: 3px solid var(--border-color);
    overflow: hidden;
    box-shadow: 
        0 15px 40px rgba(0, 0, 0, 0.5),
        inset 0 0 30px rgba(255, 193, 7, 0.1);
}

#rocketCanvas {
    width: 100%;
    height: 500px;
    display: block;
}

.game-overlay-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.multiplier-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 
        0 0 20px rgba(255, 193, 7, 0.8),
        0 0 40px rgba(255, 193, 7, 0.6);
    animation: pulse 2s ease-in-out infinite;
}

.game-status {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    border: 2px solid var(--border-color);
}

/* Game Controls */
.control-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    height: 100%;
}

.control-card h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.form-control {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
}

.form-control:focus {
    background: rgba(0, 0, 0, 0.7);
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);
    color: var(--text-primary);
}

.input-group-text {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid var(--border-color);
    color: var(--primary-color);
    font-weight: 600;
}

.potential-win {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--success-color);
    text-align: center;
    font-size: 1.1rem;
}

/* Sidebar */
.sidebar-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.sidebar-card h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.game-history {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item:last-child {
    border-bottom: none;
}

.crash-multiplier {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
}

.crash-multiplier.high {
    color: var(--success-color);
}

.crash-multiplier.medium {
    color: var(--warning-color);
}

.crash-multiplier.low {
    color: var(--danger-color);
}

.stats-grid {
    display: grid;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--primary-color);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--glow-color); }
    50% { box-shadow: 0 0 20px var(--glow-color), 0 0 30px var(--glow-color); }
}

/* Modal Styling */
.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid var(--border-color);
    border-radius: 15px;
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-family: 'Orbitron', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-features {
        justify-content: center;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-cards {
        flex-direction: column;
        align-items: center;
    }
    
    .multiplier-display {
        font-size: 2.5rem;
    }
    
    .prompt-features {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .login-prompt-card {
        padding: 2rem 1rem;
    }

    .rocket-icon i {
        font-size: 3rem;
    }

    .multiplier-display {
        font-size: 2rem;
    }
}

/* Promotions Section */
.promotions-section {
    background: rgba(0, 0, 0, 0.2);
}

.promo-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.promo-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(255, 193, 7, 0.2);
    border-color: var(--primary-color);
}

.promo-header {
    padding: 2rem 1.5rem 1rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, transparent 100%);
}

.promo-header i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.promo-header h4 {
    color: var(--primary-color);
    margin-bottom: 0;
}

.promo-body {
    padding: 1.5rem;
    flex: 1;
    text-align: center;
}

.bonus-amount {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
}

.promo-features {
    list-style: none;
    text-align: left;
    margin-top: 1.5rem;
}

.promo-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.promo-footer {
    padding: 1.5rem;
}

/* About Section */
.about-section {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(0, 0, 0, 0.3) 100%);
}

.about-features .feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.about-features .feature-item i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.about-features h5 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.about-stats {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.stat-box {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 193, 7, 0.2);
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2);
}

.stat-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Responsible Gaming Section */
.responsible-gaming-section {
    background: rgba(255, 193, 7, 0.05);
}

.responsible-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
}

.responsible-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.1);
}

.responsible-card .card-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.responsible-card h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.responsible-list {
    list-style: none;
    text-align: left;
    margin-top: 1.5rem;
}

.responsible-list li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.responsible-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Contact Section */
.contact-section {
    background: rgba(0, 0, 0, 0.3);
}

.contact-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 3rem;
    backdrop-filter: blur(10px);
}

.contact-method {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 193, 7, 0.2);
    transition: all 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.1);
}

.contact-method i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-method h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.contact-info i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.contact-info h6 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.9);
    border-top: 1px solid var(--border-color);
}

.footer-brand .logo {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--primary-color);
}

.footer-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: #000;
    transform: translateY(-2px);
}

.footer-divider {
    border-color: var(--border-color);
    margin: 2rem 0;
}

.footer-copyright {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.footer-badges {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

/* Additional Responsive Design */
@media (max-width: 768px) {
    .about-features .feature-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-card {
        padding: 2rem;
    }

    .footer-badges {
        justify-content: center;
        margin-top: 1rem;
    }

    .social-links {
        justify-content: center;
    }
}
