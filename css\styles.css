/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
}

h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

.btn-success {
    background: linear-gradient(45deg, #00b894, #00a085);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 184, 148, 0.3);
}

#cashOutBtn:not(:disabled) {
    animation: cashOutPulse 1s ease-in-out infinite;
}

@keyframes cashOutPulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(0, 184, 148, 0.6);
    }
}

.btn-outline {
    background: transparent;
    border: 2px solid #ff6b6b;
    color: #ff6b6b;
}

.btn-outline:hover {
    background: #ff6b6b;
    color: white;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.2rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 107, 107, 0.3);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: #ff6b6b;
}

.logo i {
    margin-right: 10px;
    font-size: 2rem;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #ff6b6b;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    height: 2px;
    background: #ff6b6b;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.balance {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #00b894;
    margin-right: 1rem;
}

/* Main Content */
.main {
    margin-top: 80px;
}

/* Hero Section */
.hero {
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
    z-index: -1;
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
}

.highlight {
    color: #ff6b6b;
    text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-animation {
    margin-top: 3rem;
}

.rocket-preview {
    font-size: 4rem;
    color: #ff6b6b;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Game Section */
.game-section {
    padding: 80px 0;
    background: rgba(0, 0, 0, 0.3);
}

.game-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.game-area {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.game-display {
    position: relative;
    margin-bottom: 2rem;
}

#gameCanvas {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #000428 0%, #004e92 100%);
    border-radius: 15px;
    border: 3px solid rgba(255, 107, 107, 0.5);
    box-shadow:
        0 0 20px rgba(255, 107, 107, 0.3),
        inset 0 0 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#gameCanvas:hover {
    border-color: rgba(255, 107, 107, 0.8);
    box-shadow:
        0 0 30px rgba(255, 107, 107, 0.5),
        inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.multiplier-display {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.9);
    padding: 15px 25px;
    border-radius: 30px;
    border: 3px solid #ff6b6b;
    box-shadow:
        0 0 20px rgba(255, 107, 107, 0.6),
        inset 0 0 10px rgba(255, 107, 107, 0.1);
    backdrop-filter: blur(10px);
    animation: multiplierPulse 2s ease-in-out infinite;
    display: none; /* Hide the original display since we're showing it in center */
}

@keyframes multiplierPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 0 20px rgba(255, 107, 107, 0.6),
            inset 0 0 10px rgba(255, 107, 107, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 0 30px rgba(255, 107, 107, 0.8),
            inset 0 0 15px rgba(255, 107, 107, 0.2);
    }
}

#currentMultiplier {
    font-family: 'Orbitron', monospace;
    font-size: 2.2rem;
    font-weight: 900;
    color: #ff6b6b;
    text-shadow:
        0 0 10px rgba(255, 107, 107, 0.8),
        0 0 20px rgba(255, 107, 107, 0.6),
        0 0 30px rgba(255, 107, 107, 0.4);
    animation: multiplierGlow 1.5s ease-in-out infinite alternate;
}

@keyframes multiplierGlow {
    from {
        text-shadow:
            0 0 10px rgba(255, 107, 107, 0.8),
            0 0 20px rgba(255, 107, 107, 0.6),
            0 0 30px rgba(255, 107, 107, 0.4);
    }
    to {
        text-shadow:
            0 0 15px rgba(255, 107, 107, 1),
            0 0 25px rgba(255, 107, 107, 0.8),
            0 0 35px rgba(255, 107, 107, 0.6);
    }
}

.game-status {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
}

.game-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.bet-section,
.cash-out-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.bet-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

#betAmount {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(255, 107, 107, 0.3);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1rem;
    margin-bottom: 1rem;
}

#betAmount:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
}

.potential-win {
    margin-top: 1rem;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #00b894;
    text-align: center;
}

/* Game Info Sidebar */
.game-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.game-history,
.game-stats {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item:last-child {
    border-bottom: none;
}

.crash-multiplier {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
}

.crash-multiplier.high {
    color: #00b894;
}

.crash-multiplier.medium {
    color: #fdcb6e;
}

.crash-multiplier.low {
    color: #ff6b6b;
}

.stats-grid {
    display: grid;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.stat-label {
    opacity: 0.8;
}

.stat-value {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #ff6b6b;
}

/* Responsible Gaming Section */
.responsible-section {
    padding: 80px 0;
    background: rgba(255, 107, 107, 0.05);
}

.responsible-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.warning-box {
    background: rgba(255, 107, 107, 0.1);
    border: 2px solid #ff6b6b;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

.warning-box i {
    font-size: 3rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.guidelines,
.help-resources {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.guidelines ul,
.help-resources ul {
    list-style: none;
    margin-top: 1rem;
}

.guidelines li,
.help-resources li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.guidelines li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #00b894;
    font-weight: bold;
}

.help-resources li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #ff6b6b;
    font-weight: bold;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.9);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(255, 107, 107, 0.3);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ffffff;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-section a:hover {
    opacity: 1;
    color: #ff6b6b;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    margin: 5% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    border: 2px solid rgba(255, 107, 107, 0.3);
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 15px;
    right: 20px;
}

.close:hover {
    color: #ff6b6b;
}

.modal h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #ff6b6b;
}

.modal form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modal input[type="text"],
.modal input[type="email"],
.modal input[type="password"],
.modal input[type="number"] {
    padding: 12px;
    border: 2px solid rgba(255, 107, 107, 0.3);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1rem;
}

.modal input:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

.age-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.age-buttons .btn {
    flex: 1;
}

.responsible-gambling {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 107, 107, 0.3);
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav ul {
        gap: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    .hero {
        padding: 60px 0;
    }

    .game-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .game-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .responsible-content {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .user-section {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    #gameCanvas {
        height: 250px;
    }

    #currentMultiplier {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .btn-large {
        padding: 12px 20px;
        font-size: 1rem;
    }

    .logo {
        font-size: 1.2rem;
    }

    .logo i {
        font-size: 1.5rem;
    }

    .age-buttons {
        flex-direction: column;
    }

    .game-area {
        padding: 1rem;
    }

    .multiplier-display {
        top: 10px;
        left: 10px;
        padding: 5px 15px;
    }

    #currentMultiplier {
        font-size: 1.2rem;
    }
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 107, 107, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 107, 107, 0.8); }
}

.btn-primary:hover {
    animation: pulse 0.3s ease;
}

.multiplier-display {
    animation: glow 2s ease-in-out infinite;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-success {
    color: #00b894;
}

.text-danger {
    color: #ff6b6b;
}

.text-warning {
    color: #fdcb6e;
}

.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
