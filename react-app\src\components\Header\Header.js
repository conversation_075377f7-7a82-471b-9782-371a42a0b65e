import React from 'react';
import { motion } from 'framer-motion';
import { useUser } from '../../contexts/UserContext';
import './Header.css';

const Header = ({ currentSection, onNavigate, onAuth }) => {
  const { currentUser, logout } = useUser();

  const navItems = [
    { id: 'home', label: 'Home', icon: 'fas fa-home' },
    { id: 'game', label: 'Rocket Game', icon: 'fas fa-rocket' },
    { id: 'responsible', label: 'Responsible Gaming', icon: 'fas fa-shield-alt' },
  ];

  const handleLogout = () => {
    logout();
    onNavigate('home');
  };

  return (
    <motion.header 
      className="header"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container">
        <motion.div 
          className="logo"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <i className="fas fa-rocket"></i>
          <span>RocketBet</span>
        </motion.div>

        <nav className="nav">
          <ul>
            {navItems.map((item) => (
              <motion.li key={item.id}>
                <button
                  className={`nav-link ${currentSection === item.id ? 'active' : ''}`}
                  onClick={() => onNavigate(item.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <i className={item.icon}></i>
                  <span>{item.label}</span>
                </button>
              </motion.li>
            ))}
          </ul>
        </nav>

        <div className="user-section">
          {currentUser ? (
            <motion.div 
              className="user-info"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
            >
              <div className="balance">
                <i className="fas fa-coins"></i>
                <span>Balance: ${currentUser.balance.toFixed(2)}</span>
              </div>
              <div className="user-details">
                <span className="username">{currentUser.username}</span>
                <motion.button
                  className="logout-btn"
                  onClick={handleLogout}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <i className="fas fa-sign-out-alt"></i>
                  Logout
                </motion.button>
              </div>
            </motion.div>
          ) : (
            <motion.div 
              className="auth-buttons"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
            >
              <motion.button
                className="btn btn-outline"
                onClick={() => onAuth('login')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-sign-in-alt"></i>
                Login
              </motion.button>
              <motion.button
                className="btn btn-primary"
                onClick={() => onAuth('register')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-user-plus"></i>
                Register
              </motion.button>
            </motion.div>
          )}
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
