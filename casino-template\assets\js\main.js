// Main JavaScript for RocketBet Casino Template

class UserManager {
    constructor() {
        this.currentUser = null;
        this.users = JSON.parse(localStorage.getItem('casinoUsers') || '{}');
        this.initializeEventListeners();
        this.checkAgeVerification();
    }
    
    checkAgeVerification() {
        const ageVerified = localStorage.getItem('ageVerified');
        if (!ageVerified) {
            const ageModal = new bootstrap.Modal(document.getElementById('ageVerificationModal'), {
                backdrop: 'static',
                keyboard: false
            });
            ageModal.show();
        }
    }
    
    initializeEventListeners() {
        // Age verification
        document.getElementById('confirmAge')?.addEventListener('click', () => {
            localStorage.setItem('ageVerified', 'true');
            bootstrap.Modal.getInstance(document.getElementById('ageVerificationModal')).hide();
        });
        
        document.getElementById('denyAge')?.addEventListener('click', () => {
            alert('You must be 18 or older to access this site.');
            window.location.href = 'https://www.google.com';
        });
        
        // Navigation
        this.initializeNavigation();
        
        // Auth forms
        this.initializeAuthForms();
        
        // Play now button
        document.getElementById('playNowBtn')?.addEventListener('click', () => {
            if (!this.currentUser) {
                const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
                loginModal.show();
            } else {
                scrollToRocketGame();
            }
        });
        
        // Logout
        document.getElementById('logoutBtn')?.addEventListener('click', () => {
            this.logout();
        });
    }
    
    initializeNavigation() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-link[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                    
                    // Update active nav link
                    document.querySelectorAll('.nav-link').forEach(navLink => {
                        navLink.classList.remove('active');
                    });
                    link.classList.add('active');
                }
            });
        });
        
        // Update active nav on scroll
        window.addEventListener('scroll', () => {
            this.updateActiveNavOnScroll();
            this.updateNavbarOnScroll();
        });
    }
    
    updateActiveNavOnScroll() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                document.querySelector(`.nav-link[href="#${sectionId}"]`)?.classList.add('active');
            }
        });
    }
    
    updateNavbarOnScroll() {
        const navbar = document.getElementById('mainNavbar');
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    initializeAuthForms() {
        // Create login modal HTML
        this.createAuthModals();
        
        // Login form
        document.getElementById('loginForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        // Register form
        document.getElementById('registerForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });
        
        // Switch between login and register
        document.getElementById('showRegister')?.addEventListener('click', (e) => {
            e.preventDefault();
            bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            registerModal.show();
        });
        
        document.getElementById('showLogin')?.addEventListener('click', (e) => {
            e.preventDefault();
            bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        });
    }
    
    createAuthModals() {
        // Create login modal
        const loginModalHTML = `
            <div class="modal fade" id="loginModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title text-warning">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to RocketBet
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="loginUsername" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="loginUsername" required>
                                </div>
                                <div class="mb-3">
                                    <label for="loginPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </form>
                            <div class="text-center mt-3">
                                <p class="mb-0">Don't have an account? 
                                    <a href="#" id="showRegister" class="text-warning">Register here</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Create register modal
        const registerModalHTML = `
            <div class="modal fade" id="registerModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title text-warning">
                                <i class="fas fa-user-plus me-2"></i>Join RocketBet
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="registerForm">
                                <div class="mb-3">
                                    <label for="registerUsername" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="registerUsername" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="ageConfirm" required>
                                    <label class="form-check-label" for="ageConfirm">
                                        I confirm that I am 18 years or older
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="termsAccept" required>
                                    <label class="form-check-label" for="termsAccept">
                                        I accept the Terms of Service
                                    </label>
                                </div>
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </form>
                            <div class="text-center mt-3">
                                <p class="mb-0">Already have an account? 
                                    <a href="#" id="showLogin" class="text-warning">Login here</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modals to body
        document.body.insertAdjacentHTML('beforeend', loginModalHTML);
        document.body.insertAdjacentHTML('beforeend', registerModalHTML);
    }
    
    handleLogin() {
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;
        
        if (!username || !password) {
            this.showAlert('Please fill in all fields', 'danger');
            return;
        }
        
        const user = this.users[username];
        if (!user || user.password !== password) {
            this.showAlert('Invalid username or password', 'danger');
            return;
        }
        
        this.currentUser = user;
        this.updateUserInterface();
        bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
        
        // Clear form
        document.getElementById('loginForm').reset();
        
        this.showAlert(`Welcome back, ${user.username}!`, 'success');
        
        // Initialize rocket game
        setTimeout(() => {
            if (window.checkUserAndStartGame) {
                window.checkUserAndStartGame();
            }
        }, 500);
    }
    
    handleRegister() {
        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const ageConfirm = document.getElementById('ageConfirm').checked;
        const termsAccept = document.getElementById('termsAccept').checked;
        
        // Validation
        if (!username || !email || !password || !confirmPassword) {
            this.showAlert('Please fill in all fields', 'danger');
            return;
        }
        
        if (password !== confirmPassword) {
            this.showAlert('Passwords do not match', 'danger');
            return;
        }
        
        if (password.length < 6) {
            this.showAlert('Password must be at least 6 characters long', 'danger');
            return;
        }
        
        if (!ageConfirm) {
            this.showAlert('You must confirm that you are 18 or older', 'danger');
            return;
        }
        
        if (!termsAccept) {
            this.showAlert('You must accept the Terms of Service', 'danger');
            return;
        }
        
        if (this.users[username]) {
            this.showAlert('Username already exists', 'danger');
            return;
        }
        
        // Create new user
        const newUser = {
            username: username,
            email: email,
            password: password,
            balance: 1000.00,
            registeredAt: new Date().toISOString(),
            stats: {
                gamesPlayed: 0,
                totalWagered: 0,
                totalWon: 0
            }
        };
        
        this.users[username] = newUser;
        this.saveUsers();
        
        this.currentUser = newUser;
        this.updateUserInterface();
        bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
        
        // Clear form
        document.getElementById('registerForm').reset();
        
        this.showAlert(`Welcome to RocketBet, ${username}! You've been given $1000 to start playing.`, 'success');
        
        // Initialize rocket game
        setTimeout(() => {
            if (window.checkUserAndStartGame) {
                window.checkUserAndStartGame();
            }
        }, 500);
    }
    
    logout() {
        this.currentUser = null;
        this.updateUserInterface();
        this.showAlert('You have been logged out successfully.', 'info');
        
        // Hide game interface and show login prompt
        document.getElementById('gameInterface')?.classList.add('d-none');
        document.getElementById('gameLoginPrompt')?.classList.remove('d-none');
    }
    
    updateUserInterface() {
        const userSection = document.getElementById('userSection');
        const authButtons = document.getElementById('authButtons');
        const usernameElement = document.getElementById('username');
        const balanceElement = document.getElementById('userBalance');
        
        if (this.currentUser) {
            userSection?.classList.remove('d-none');
            authButtons?.classList.add('d-none');
            
            if (usernameElement) usernameElement.textContent = this.currentUser.username;
            if (balanceElement) balanceElement.textContent = this.currentUser.balance.toFixed(2);
        } else {
            userSection?.classList.add('d-none');
            authButtons?.classList.remove('d-none');
        }
    }
    
    updateUserBalance(newBalance) {
        if (this.currentUser) {
            this.currentUser.balance = newBalance;
            this.users[this.currentUser.username] = this.currentUser;
            this.saveUsers();
            
            const balanceElement = document.getElementById('userBalance');
            if (balanceElement) {
                balanceElement.textContent = newBalance.toFixed(2);
            }
        }
    }
    
    getCurrentUser() {
        return this.currentUser;
    }
    
    saveUsers() {
        localStorage.setItem('casinoUsers', JSON.stringify(this.users));
    }
    
    showAlert(message, type = 'info') {
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                 style="top: 100px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', alertHTML);
        
        // Auto remove after 4 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.textContent.includes(message)) {
                    alert.remove();
                }
            });
        }, 4000);
    }
}

// Responsible Gaming Manager
class ResponsibleGamingManager {
    constructor() {
        this.sessionStartTime = Date.now();
        this.sessionBetAmount = 0;
        this.dailyLimit = 500;
        this.sessionLimit = 2 * 60 * 60 * 1000; // 2 hours
        
        this.initializeWarnings();
    }
    
    initializeWarnings() {
        // Check session time every 30 minutes
        setInterval(() => {
            this.checkSessionTime();
        }, 30 * 60 * 1000);
    }
    
    checkSessionTime() {
        const sessionDuration = Date.now() - this.sessionStartTime;
        
        if (sessionDuration > this.sessionLimit) {
            alert('You have been playing for over 2 hours. Consider taking a break!');
            this.sessionStartTime = Date.now();
        }
    }
    
    recordBet(amount) {
        const today = new Date().toDateString();
        const dailyBets = JSON.parse(localStorage.getItem('dailyBets') || '{}');
        
        dailyBets[today] = (dailyBets[today] || 0) + amount;
        localStorage.setItem('dailyBets', JSON.stringify(dailyBets));
        
        this.sessionBetAmount += amount;
        
        if (dailyBets[today] > this.dailyLimit * 0.8) {
            alert('Warning: You are approaching your daily betting limit. Please gamble responsibly.');
        }
    }
}

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }
    
    // Initialize managers
    window.userManager = new UserManager();
    window.responsibleGaming = new ResponsibleGamingManager();
    
    // Add smooth scrolling for all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
