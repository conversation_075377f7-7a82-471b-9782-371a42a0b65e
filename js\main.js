class UserManager {
    constructor() {
        this.currentUser = null;
        this.users = JSON.parse(localStorage.getItem('gamblingUsers') || '{}');
        this.initializeEventListeners();
        this.checkAgeVerification();
    }

    checkAgeVerification() {
        const ageVerified = localStorage.getItem('ageVerified');
        if (!ageVerified) {
            this.showAgeVerification();
        }
    }

    showAgeVerification() {
        const modal = document.getElementById('ageVerificationModal');
        modal.style.display = 'block';

        document.getElementById('confirmAge').addEventListener('click', () => {
            localStorage.setItem('ageVerified', 'true');
            modal.style.display = 'none';
        });

        document.getElementById('denyAge').addEventListener('click', () => {
            alert('You must be 18 or older to access this site.');
            window.location.href = 'https://www.google.com';
        });
    }

    initializeEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToSection(link.getAttribute('href').substring(1));
            });
        });

        // Play Now button
        document.getElementById('playNowBtn').addEventListener('click', () => {
            if (!this.currentUser) {
                this.showLoginModal();
            } else {
                this.navigateToSection('game');
            }
        });

        // Auth buttons
        document.getElementById('loginBtn').addEventListener('click', () => this.showLoginModal());
        document.getElementById('registerBtn').addEventListener('click', () => this.showRegisterModal());
        document.getElementById('logoutBtn').addEventListener('click', () => this.logout());

        // Modal controls
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });

        // Switch between login and register
        document.getElementById('showRegister').addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('loginModal').style.display = 'none';
            document.getElementById('registerModal').style.display = 'block';
        });

        document.getElementById('showLogin').addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('registerModal').style.display = 'none';
            document.getElementById('loginModal').style.display = 'block';
        });

        // Form submissions
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    navigateToSection(sectionId) {
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[href="#${sectionId}"]`).classList.add('active');

        // Scroll to section
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
        }
    }

    showLoginModal() {
        document.getElementById('loginModal').style.display = 'block';
    }

    showRegisterModal() {
        document.getElementById('registerModal').style.display = 'block';
    }

    handleLogin() {
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        if (!username || !password) {
            alert('Please fill in all fields');
            return;
        }

        const user = this.users[username];
        if (!user || user.password !== password) {
            alert('Invalid username or password');
            return;
        }

        this.currentUser = user;
        this.updateUserInterface();
        document.getElementById('loginModal').style.display = 'none';

        // Clear form
        document.getElementById('loginForm').reset();

        // Initialize the rocket game
        setTimeout(() => {
            if (window.initializeRocketGame) {
                window.initializeRocketGame();
            }
        }, 500);

        alert(`Welcome back, ${user.username}!`);
    }

    handleRegister() {
        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const ageConfirm = document.getElementById('ageConfirm').checked;
        const termsAccept = document.getElementById('termsAccept').checked;

        // Validation
        if (!username || !email || !password || !confirmPassword) {
            alert('Please fill in all fields');
            return;
        }

        if (password !== confirmPassword) {
            alert('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            alert('Password must be at least 6 characters long');
            return;
        }

        if (!ageConfirm) {
            alert('You must confirm that you are 18 or older');
            return;
        }

        if (!termsAccept) {
            alert('You must accept the Terms of Service');
            return;
        }

        if (this.users[username]) {
            alert('Username already exists');
            return;
        }

        // Create new user
        const newUser = {
            username: username,
            email: email,
            password: password,
            balance: 1000.00, // Starting balance
            registeredAt: new Date().toISOString()
        };

        this.users[username] = newUser;
        this.saveUsers();

        this.currentUser = newUser;
        this.updateUserInterface();
        document.getElementById('registerModal').style.display = 'none';

        // Clear form
        document.getElementById('registerForm').reset();

        // Initialize the rocket game
        setTimeout(() => {
            if (window.initializeRocketGame) {
                window.initializeRocketGame();
            }
        }, 500);

        alert(`Welcome to RocketBet, ${username}! You've been given $1000 to start playing.`);
    }

    logout() {
        this.currentUser = null;
        this.updateUserInterface();
        alert('You have been logged out successfully.');

        // Navigate back to home
        this.navigateToSection('home');
    }

    updateUserInterface() {
        const userInfo = document.getElementById('userInfo');
        const authButtons = document.getElementById('authButtons');

        if (this.currentUser) {
            // Show user info, hide auth buttons
            userInfo.style.display = 'flex';
            authButtons.style.display = 'none';

            // Update balance display
            document.getElementById('userBalance').textContent = this.currentUser.balance.toFixed(2);
        } else {
            // Hide user info, show auth buttons
            userInfo.style.display = 'none';
            authButtons.style.display = 'flex';
        }
    }

    saveUsers() {
        localStorage.setItem('gamblingUsers', JSON.stringify(this.users));
    }

    updateUserBalance(newBalance) {
        if (this.currentUser) {
            this.currentUser.balance = newBalance;
            this.users[this.currentUser.username] = this.currentUser;
            this.saveUsers();
            document.getElementById('userBalance').textContent = newBalance.toFixed(2);
        }
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Responsible Gaming Features
class ResponsibleGaming {
    constructor() {
        this.sessionStartTime = Date.now();
        this.sessionBetAmount = 0;
        this.dailyLimit = 500; // Daily betting limit
        this.sessionLimit = 2 * 60 * 60 * 1000; // 2 hours in milliseconds

        this.initializeWarnings();
    }

    initializeWarnings() {
        // Check session time every 30 minutes
        setInterval(() => {
            this.checkSessionTime();
        }, 30 * 60 * 1000);

        // Check daily limits
        this.checkDailyLimits();
    }

    checkSessionTime() {
        const sessionDuration = Date.now() - this.sessionStartTime;

        if (sessionDuration > this.sessionLimit) {
            alert('You have been playing for over 2 hours. Consider taking a break!');
            this.sessionStartTime = Date.now(); // Reset timer
        }
    }

    checkDailyLimits() {
        const today = new Date().toDateString();
        const dailyBets = JSON.parse(localStorage.getItem('dailyBets') || '{}');

        if (dailyBets[today] && dailyBets[today] > this.dailyLimit) {
            alert('You have reached your daily betting limit of $500. Please play responsibly.');
            return false;
        }

        return true;
    }

    recordBet(amount) {
        const today = new Date().toDateString();
        const dailyBets = JSON.parse(localStorage.getItem('dailyBets') || '{}');

        dailyBets[today] = (dailyBets[today] || 0) + amount;
        localStorage.setItem('dailyBets', JSON.stringify(dailyBets));

        this.sessionBetAmount += amount;

        // Warn if approaching limits
        if (dailyBets[today] > this.dailyLimit * 0.8) {
            alert('Warning: You are approaching your daily betting limit. Please gamble responsibly.');
        }
    }
}

// Initialize the application
let userManager;
let responsibleGaming;

document.addEventListener('DOMContentLoaded', function() {
    userManager = new UserManager();
    responsibleGaming = new ResponsibleGaming();

    // Make them globally available
    window.userManager = userManager;
    window.responsibleGaming = responsibleGaming;

    // Add smooth scrolling for all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading animation
    document.body.classList.add('fade-in');
});
