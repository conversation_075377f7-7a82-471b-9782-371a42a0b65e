import React, { useRef, useEffect, useCallback } from 'react';
import { useGame } from '../../contexts/GameContext';

const GameCanvas = () => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const { gameState, multiplier, rocket, elapsedTime, updateRocketTrail } = useGame();

  const drawAnimatedBackground = useCallback((ctx, canvas, time) => {
    // Create animated gradient background
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    
    // Animated colors
    const r1 = Math.sin(time * 0.5) * 20 + 10;
    const g1 = Math.sin(time * 0.3) * 30 + 40;
    const b1 = Math.sin(time * 0.7) * 40 + 80;
    
    gradient.addColorStop(0, `rgb(${r1}, ${g1}, ${b1})`);
    gradient.addColorStop(0.5, '#001122');
    gradient.addColorStop(1, '#004e92');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }, []);

  const drawTwinklingStars = useCallback((ctx, canvas, time) => {
    for (let i = 0; i < 150; i++) {
      const x = (i * 37) % canvas.width;
      const y = (i * 23) % (canvas.height * 0.7);
      const twinkle = Math.sin(time * 2 + i) * 0.5 + 0.5;
      const size = (Math.sin(i) * 0.5 + 1) * twinkle;
      
      ctx.fillStyle = `rgba(255, 255, 255, ${twinkle})`;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
      
      // Add star glow for bright stars
      if (twinkle > 0.7) {
        ctx.fillStyle = `rgba(255, 255, 255, ${(twinkle - 0.7) * 0.3})`;
        ctx.beginPath();
        ctx.arc(x, y, size * 3, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }, []);

  const drawClouds = useCallback((ctx, canvas, time) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    
    for (let i = 0; i < 6; i++) {
      const x = ((i * 150 + time * 20) % (canvas.width + 100)) - 50;
      const y = 50 + i * 30 + Math.sin(time + i) * 10;
      
      ctx.beginPath();
      ctx.arc(x, y, 30, 0, Math.PI * 2);
      ctx.arc(x + 25, y, 35, 0, Math.PI * 2);
      ctx.arc(x + 50, y, 30, 0, Math.PI * 2);
      ctx.fill();
    }
  }, []);

  const drawEnhancedRocket = useCallback((ctx, time) => {
    ctx.save();
    ctx.translate(rocket.x, rocket.y);
    ctx.rotate(rocket.angle);
    
    // Draw rocket shadow
    ctx.save();
    ctx.translate(3, 3);
    ctx.globalAlpha = 0.3;
    ctx.fillStyle = '#000000';
    ctx.fillRect(-20, -8, 40, 16);
    ctx.restore();
    
    // Draw rocket body with gradient
    const bodyGradient = ctx.createLinearGradient(-15, -8, -15, 8);
    bodyGradient.addColorStop(0, '#ff6b6b');
    bodyGradient.addColorStop(0.5, '#ff8e53');
    bodyGradient.addColorStop(1, '#ff6b6b');
    ctx.fillStyle = bodyGradient;
    ctx.fillRect(-15, -6, 30, 12);
    
    // Draw rocket details
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(-10, -4, 20, 2);
    ctx.fillRect(-10, 2, 20, 2);
    
    // Draw rocket nose with gradient
    const noseGradient = ctx.createLinearGradient(15, -6, 25, 0);
    noseGradient.addColorStop(0, '#ffeb3b');
    noseGradient.addColorStop(1, '#ffc107');
    ctx.fillStyle = noseGradient;
    ctx.beginPath();
    ctx.moveTo(15, 0);
    ctx.lineTo(25, -6);
    ctx.lineTo(25, 6);
    ctx.closePath();
    ctx.fill();
    
    // Draw rocket fins
    const finGradient = ctx.createLinearGradient(-15, -10, -7, -2);
    finGradient.addColorStop(0, '#ee5a24');
    finGradient.addColorStop(1, '#fd79a8');
    ctx.fillStyle = finGradient;
    ctx.fillRect(-15, -10, 8, 8);
    ctx.fillRect(-15, 2, 8, 8);
    
    // Draw enhanced flame effect
    if (gameState === 'flying') {
      // Main flame
      const flameGradient = ctx.createLinearGradient(-30, 0, -15, 0);
      flameGradient.addColorStop(0, '#ffeb3b');
      flameGradient.addColorStop(0.5, '#ff9800');
      flameGradient.addColorStop(1, '#ff5722');
      
      ctx.fillStyle = flameGradient;
      const flameLength = 15 + Math.sin(time) * 5;
      ctx.fillRect(-15 - flameLength, -4, flameLength, 8);
      
      // Inner flame
      ctx.fillStyle = '#ffeb3b';
      const innerFlameLength = 8 + Math.sin(time * 1.5) * 3;
      ctx.fillRect(-15 - innerFlameLength, -2, innerFlameLength, 4);
      
      // Flame particles
      for (let i = 0; i < 8; i++) {
        const particleX = -25 - Math.random() * 15;
        const particleY = (Math.random() - 0.5) * 10;
        const particleSize = Math.random() * 3 + 1;
        
        ctx.fillStyle = `rgba(255, ${Math.floor(Math.random() * 100 + 155)}, 0, ${Math.random()})`;
        ctx.beginPath();
        ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
        ctx.fill();
      }
    }
    
    // Draw rocket glow
    if (gameState === 'flying') {
      const glowIntensity = Math.min(multiplier / 5, 1);
      ctx.shadowColor = '#ff6b6b';
      ctx.shadowBlur = 20 * glowIntensity;
      ctx.fillStyle = `rgba(255, 107, 107, ${0.1 * glowIntensity})`;
      ctx.fillRect(-25, -12, 50, 24);
      ctx.shadowBlur = 0;
    }
    
    ctx.restore();
  }, [rocket, gameState, multiplier]);

  const drawRocketTrail = useCallback((ctx) => {
    if (rocket.trail.length < 2) return;
    
    const trailLayers = [
      { color: '#ff6b6b', width: 8, alpha: 0.4 },
      { color: '#ff8e53', width: 5, alpha: 0.6 },
      { color: '#ffeb3b', width: 2, alpha: 0.8 }
    ];
    
    trailLayers.forEach(layer => {
      ctx.strokeStyle = layer.color;
      ctx.lineWidth = layer.width;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      
      ctx.beginPath();
      ctx.moveTo(rocket.trail[0].x, rocket.trail[0].y);
      
      for (let i = 1; i < rocket.trail.length; i++) {
        const point = rocket.trail[i];
        const alpha = (i / rocket.trail.length) * layer.alpha;
        ctx.globalAlpha = alpha;
        ctx.lineTo(point.x, point.y);
      }
      
      ctx.stroke();
      ctx.globalAlpha = 1;
    });
  }, [rocket.trail]);

  const drawEnhancedGround = useCallback((ctx, canvas) => {
    const groundHeight = 80;
    const groundY = canvas.height - groundHeight;
    
    // Draw ground gradient
    const groundGradient = ctx.createLinearGradient(0, groundY, 0, canvas.height);
    groundGradient.addColorStop(0, '#2d3436');
    groundGradient.addColorStop(0.5, '#636e72');
    groundGradient.addColorStop(1, '#2d3436');
    ctx.fillStyle = groundGradient;
    ctx.fillRect(0, groundY, canvas.width, groundHeight);
    
    // Draw city skyline
    const buildings = [
      { x: 150, width: 60, height: 120 },
      { x: 220, width: 40, height: 80 },
      { x: 270, width: 80, height: 150 },
      { x: 360, width: 50, height: 100 },
      { x: 420, width: 70, height: 130 },
      { x: 500, width: 45, height: 90 },
      { x: 560, width: 90, height: 160 },
      { x: 660, width: 55, height: 110 }
    ];
    
    buildings.forEach(building => {
      const buildingY = groundY - building.height;
      
      // Building gradient
      const buildingGradient = ctx.createLinearGradient(
        building.x, buildingY, 
        building.x, groundY
      );
      buildingGradient.addColorStop(0, '#34495e');
      buildingGradient.addColorStop(1, '#2c3e50');
      
      ctx.fillStyle = buildingGradient;
      ctx.fillRect(building.x, buildingY, building.width, building.height);
      
      // Building windows
      ctx.fillStyle = '#f39c12';
      const windowRows = Math.floor(building.height / 15);
      const windowCols = Math.floor(building.width / 12);
      
      for (let row = 0; row < windowRows; row++) {
        for (let col = 0; col < windowCols; col++) {
          if (Math.random() > 0.3) {
            const windowX = building.x + 4 + col * 12;
            const windowY = buildingY + 4 + row * 15;
            ctx.fillRect(windowX, windowY, 6, 8);
          }
        }
      }
    });
    
    // Draw enhanced launch pad
    const padX = 30;
    const padY = groundY - 20;
    const padWidth = 40;
    const padHeight = 20;
    
    const padGradient = ctx.createLinearGradient(padX, padY, padX, padY + padHeight);
    padGradient.addColorStop(0, '#74b9ff');
    padGradient.addColorStop(1, '#0984e3');
    ctx.fillStyle = padGradient;
    ctx.fillRect(padX, padY, padWidth, padHeight);
    
    // Launch pad details
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(padX + 5, padY + 5, padWidth - 10, 2);
    ctx.fillRect(padX + 5, padY + 10, padWidth - 10, 2);
    ctx.fillRect(padX + 5, padY + 15, padWidth - 10, 2);
  }, []);

  const render = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const time = Date.now() * 0.001;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw all elements
    drawAnimatedBackground(ctx, canvas, time);
    drawTwinklingStars(ctx, canvas, time);
    drawClouds(ctx, canvas, time);
    drawRocketTrail(ctx);
    drawEnhancedRocket(ctx, time);
    drawEnhancedGround(ctx, canvas);
    
    // Update rocket trail
    if (gameState === 'flying') {
      updateRocketTrail({ x: rocket.x, y: rocket.y, time: elapsedTime });
    }
  }, [
    drawAnimatedBackground,
    drawTwinklingStars,
    drawClouds,
    drawRocketTrail,
    drawEnhancedRocket,
    drawEnhancedGround,
    gameState,
    rocket,
    elapsedTime,
    updateRocketTrail
  ]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    // Set canvas size
    canvas.width = 900;
    canvas.height = 500;
    
    const animate = () => {
      render();
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [render]);

  return (
    <canvas
      ref={canvasRef}
      className="game-canvas"
      style={{
        width: '100%',
        height: '500px',
        borderRadius: '15px',
        background: 'linear-gradient(135deg, #000428 0%, #004e92 100%)',
      }}
    />
  );
};

export default GameCanvas;
