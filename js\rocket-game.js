class RocketGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameState = 'waiting'; // waiting, countdown, flying, crashed
        this.multiplier = 1.00;
        this.crashPoint = 0;
        this.startTime = 0;
        this.currentBet = 0;
        this.hasActiveBet = false;
        this.hasCashedOut = false;
        this.gameHistory = [];
        this.animationId = null;

        // Rocket properties
        this.rocket = {
            x: 50,
            y: this.canvas.height - 100,
            baseY: this.canvas.height - 100,
            angle: 0,
            trail: []
        };

        // Game settings
        this.countdownTime = 5000; // 5 seconds
        this.minCrashPoint = 1.01;
        this.maxCrashPoint = 50.0;

        this.initializeCanvas();
        this.bindEvents();
        this.startNewRound();
    }

    initializeCanvas() {
        // Set canvas size
        this.canvas.width = 800;
        this.canvas.height = 400;

        // Set initial rocket position
        this.rocket.y = this.canvas.height - 100;
        this.rocket.baseY = this.canvas.height - 100;
    }

    bindEvents() {
        const placeBetBtn = document.getElementById('placeBetBtn');
        const cashOutBtn = document.getElementById('cashOutBtn');
        const betAmountInput = document.getElementById('betAmount');

        console.log('Binding events to buttons:', {
            placeBetBtn: !!placeBetBtn,
            cashOutBtn: !!cashOutBtn,
            betAmountInput: !!betAmountInput
        });

        if (placeBetBtn) {
            placeBetBtn.addEventListener('click', () => {
                console.log('Place bet button clicked');
                this.placeBet();
            });
        } else {
            console.error('Place bet button not found!');
        }

        if (cashOutBtn) {
            cashOutBtn.addEventListener('click', () => {
                console.log('Cash out button clicked');
                this.cashOut();
            });
        }

        if (betAmountInput) {
            betAmountInput.addEventListener('input', () => this.updatePotentialWin());
        }
    }

    generateCrashPoint() {
        // Generate crash point using exponential distribution
        // This creates realistic crash patterns where lower multipliers are more common
        const random = Math.random();
        const crashPoint = Math.max(
            this.minCrashPoint,
            Math.min(
                this.maxCrashPoint,
                1 / (1 - random * 0.99) // Exponential distribution
            )
        );
        return Math.round(crashPoint * 100) / 100;
    }

    placeBet() {
        console.log('Place bet clicked, game state:', this.gameState);

        if (this.gameState !== 'waiting') {
            alert(`Cannot place bet. Game state: ${this.gameState}`);
            return;
        }

        const betAmount = parseFloat(document.getElementById('betAmount').value);
        const userBalance = parseFloat(document.getElementById('userBalance').textContent);

        console.log('Bet amount:', betAmount, 'User balance:', userBalance);

        if (isNaN(betAmount) || betAmount <= 0) {
            alert('Please enter a valid bet amount!');
            return;
        }

        if (betAmount > userBalance) {
            alert('Insufficient balance!');
            return;
        }

        // Record bet for responsible gaming
        if (window.responsibleGaming) {
            window.responsibleGaming.recordBet(betAmount);
        }

        this.currentBet = betAmount;
        this.hasActiveBet = true;
        this.hasCashedOut = false;

        // Update balance
        this.updateBalance(-betAmount);

        // Update UI
        document.getElementById('placeBetBtn').disabled = true;
        document.getElementById('cashOutBtn').disabled = false;

        this.updateGameStatus('Bet placed! Waiting for round to start...');
        console.log('Bet placed successfully');
    }

    cashOut() {
        if (!this.hasActiveBet || this.hasCashedOut || this.gameState !== 'flying') return;

        this.hasCashedOut = true;
        const winAmount = this.currentBet * this.multiplier;

        // Update balance
        this.updateBalance(winAmount);

        // Update UI
        document.getElementById('cashOutBtn').disabled = true;

        // Update stats
        this.updateStats(this.currentBet, winAmount);

        this.updateGameStatus(`Cashed out at ${this.multiplier.toFixed(2)}x! Won $${winAmount.toFixed(2)}`);
    }

    updateBalance(amount) {
        const balanceElement = document.getElementById('userBalance');
        const currentBalance = parseFloat(balanceElement.textContent);
        const newBalance = Math.max(0, currentBalance + amount);
        balanceElement.textContent = newBalance.toFixed(2);

        // Update user manager if available
        if (window.userManager && window.userManager.getCurrentUser()) {
            window.userManager.updateUserBalance(newBalance);
        }
    }

    updatePotentialWin() {
        const betAmount = parseFloat(document.getElementById('betAmount').value) || 0;
        const potentialWin = betAmount * this.multiplier;
        document.getElementById('potentialWin').textContent = potentialWin.toFixed(2);
    }

    updateGameStatus(message) {
        document.getElementById('gameStatus').textContent = message;
    }

    updateMultiplierDisplay() {
        document.getElementById('currentMultiplier').textContent = this.multiplier.toFixed(2) + 'x';
        this.updatePotentialWin();
    }

    updateStats(wagered, won) {
        // Update games played
        const gamesPlayedElement = document.getElementById('gamesPlayed');
        const gamesPlayed = parseInt(gamesPlayedElement.textContent) + 1;
        gamesPlayedElement.textContent = gamesPlayed;

        // Update total wagered
        const totalWageredElement = document.getElementById('totalWagered');
        const totalWagered = parseFloat(totalWageredElement.textContent) + wagered;
        totalWageredElement.textContent = totalWagered.toFixed(2);

        // Update total won
        const totalWonElement = document.getElementById('totalWon');
        const totalWon = parseFloat(totalWonElement.textContent) + won;
        totalWonElement.textContent = totalWon.toFixed(2);
    }

    addToHistory(crashPoint) {
        this.gameHistory.unshift(crashPoint);
        if (this.gameHistory.length > 10) {
            this.gameHistory.pop();
        }

        this.updateHistoryDisplay();
    }

    updateHistoryDisplay() {
        const historyContainer = document.getElementById('gameHistory');
        historyContainer.innerHTML = '';

        this.gameHistory.forEach(crashPoint => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';

            let crashClass = 'low';
            if (crashPoint >= 2.0) crashClass = 'medium';
            if (crashPoint >= 5.0) crashClass = 'high';

            historyItem.innerHTML = `
                <span>Crashed at</span>
                <span class="crash-multiplier ${crashClass}">${crashPoint.toFixed(2)}x</span>
            `;

            historyContainer.appendChild(historyItem);
        });
    }

    startNewRound() {
        // Reset game state
        this.gameState = 'waiting';
        this.multiplier = 1.00;
        this.crashPoint = this.generateCrashPoint();
        this.hasActiveBet = false;
        this.hasCashedOut = false;
        this.currentBet = 0;

        // Reset rocket position
        this.rocket.x = 50;
        this.rocket.y = this.rocket.baseY;
        this.rocket.angle = 0;
        this.rocket.trail = [];

        // Reset UI
        document.getElementById('placeBetBtn').disabled = false;
        document.getElementById('cashOutBtn').disabled = true;
        this.updateMultiplierDisplay();
        this.updateGameStatus('Place your bet for the next round!');

        // Start countdown after a short delay
        setTimeout(() => {
            this.startCountdown();
        }, 3000);
    }

    startCountdown() {
        this.gameState = 'countdown';
        let countdown = 5;

        const countdownInterval = setInterval(() => {
            this.updateGameStatus(`Round starting in ${countdown}...`);
            countdown--;

            if (countdown < 0) {
                clearInterval(countdownInterval);
                this.startFlight();
            }
        }, 1000);
    }

    startFlight() {
        this.gameState = 'flying';
        this.startTime = Date.now();
        this.updateGameStatus('Rocket is flying! Cash out before it crashes!');

        // Disable bet placement
        document.getElementById('placeBetBtn').disabled = true;

        this.gameLoop();
    }

    gameLoop() {
        if (this.gameState !== 'flying') return;

        const currentTime = Date.now();
        const elapsedTime = (currentTime - this.startTime) / 1000; // Convert to seconds

        // Calculate multiplier based on elapsed time
        // Exponential growth that matches the crash point
        this.multiplier = Math.pow(1.1, elapsedTime * 2);

        // Check if we've reached the crash point
        if (this.multiplier >= this.crashPoint) {
            this.crash();
            return;
        }

        // Update rocket position and animation
        this.updateRocket(elapsedTime);

        // Update display
        this.updateMultiplierDisplay();

        // Render the game
        this.render();

        // Continue the game loop
        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }

    updateRocket(elapsedTime) {
        // Update rocket position
        this.rocket.x = 50 + (elapsedTime * 60); // Move right over time
        this.rocket.y = this.rocket.baseY - (elapsedTime * 40); // Move up over time
        this.rocket.angle = Math.sin(elapsedTime * 2) * 0.2; // Slight wobble

        // Add to trail
        this.rocket.trail.push({
            x: this.rocket.x,
            y: this.rocket.y,
            time: elapsedTime
        });

        // Limit trail length
        if (this.rocket.trail.length > 50) {
            this.rocket.trail.shift();
        }
    }

    crash() {
        this.gameState = 'crashed';

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        // Handle active bets
        if (this.hasActiveBet && !this.hasCashedOut) {
            // Player loses their bet
            this.updateStats(this.currentBet, 0);
            this.updateGameStatus(`Rocket crashed at ${this.crashPoint.toFixed(2)}x! You lost $${this.currentBet.toFixed(2)}`);
        } else {
            this.updateGameStatus(`Rocket crashed at ${this.crashPoint.toFixed(2)}x!`);
        }

        // Add to history
        this.addToHistory(this.crashPoint);

        // Render crash effect
        this.renderCrash();

        // Start new round after delay
        setTimeout(() => {
            this.startNewRound();
        }, 5000);
    }

    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw animated background
        this.drawAnimatedBackground();

        // Draw stars with twinkling effect
        this.drawTwinklingStars();

        // Draw clouds
        this.drawClouds();

        // Draw rocket trail with gradient
        this.drawEnhancedRocketTrail();

        // Draw enhanced rocket
        this.drawEnhancedRocket();

        // Draw enhanced ground with city skyline
        this.drawEnhancedGround();

        // Draw particles and effects
        this.drawParticleEffects();

        // Draw multiplier glow effect
        this.drawMultiplierGlow();

        // Draw multiplier scale on the right side
        this.drawMultiplierScale();

        // Draw large center multiplier
        this.drawCenterMultiplier();

        // Draw time scale at bottom
        this.drawTimeScale();
    }

    drawAnimatedBackground() {
        // Create animated gradient background
        const time = Date.now() * 0.001;
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);

        // Animated colors
        const r1 = Math.sin(time * 0.5) * 20 + 10;
        const g1 = Math.sin(time * 0.3) * 30 + 40;
        const b1 = Math.sin(time * 0.7) * 40 + 80;

        gradient.addColorStop(0, `rgb(${r1}, ${g1}, ${b1})`);
        gradient.addColorStop(0.5, '#001122');
        gradient.addColorStop(1, '#004e92');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    drawTwinklingStars() {
        const time = Date.now() * 0.001;

        for (let i = 0; i < 100; i++) {
            const x = (i * 37) % this.canvas.width;
            const y = (i * 23) % (this.canvas.height * 0.7);
            const twinkle = Math.sin(time * 2 + i) * 0.5 + 0.5;
            const size = (Math.sin(i) * 0.5 + 1) * twinkle;

            this.ctx.fillStyle = `rgba(255, 255, 255, ${twinkle})`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();

            // Add star glow
            if (twinkle > 0.7) {
                this.ctx.fillStyle = `rgba(255, 255, 255, ${(twinkle - 0.7) * 0.3})`;
                this.ctx.beginPath();
                this.ctx.arc(x, y, size * 3, 0, Math.PI * 2);
                this.ctx.fill();
            }
        }
    }

    drawClouds() {
        const time = Date.now() * 0.0005;
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';

        for (let i = 0; i < 5; i++) {
            const x = ((i * 150 + time * 20) % (this.canvas.width + 100)) - 50;
            const y = 50 + i * 30 + Math.sin(time + i) * 10;

            this.ctx.beginPath();
            this.ctx.arc(x, y, 30, 0, Math.PI * 2);
            this.ctx.arc(x + 25, y, 35, 0, Math.PI * 2);
            this.ctx.arc(x + 50, y, 30, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawEnhancedRocketTrail() {
        if (this.rocket.trail.length < 2) return;

        // Draw multiple trail layers for depth
        const trailLayers = [
            { color: '#ff6b6b', width: 8, alpha: 0.3 },
            { color: '#ff8e53', width: 5, alpha: 0.5 },
            { color: '#ffeb3b', width: 2, alpha: 0.8 }
        ];

        trailLayers.forEach(layer => {
            this.ctx.strokeStyle = layer.color;
            this.ctx.lineWidth = layer.width;
            this.ctx.globalAlpha = layer.alpha;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';

            this.ctx.beginPath();
            this.ctx.moveTo(this.rocket.trail[0].x, this.rocket.trail[0].y);

            for (let i = 1; i < this.rocket.trail.length; i++) {
                const point = this.rocket.trail[i];
                const alpha = (i / this.rocket.trail.length) * layer.alpha;
                this.ctx.globalAlpha = alpha;
                this.ctx.lineTo(point.x, point.y);
            }

            this.ctx.stroke();
        });

        this.ctx.globalAlpha = 1;
    }

    drawEnhancedRocket() {
        this.ctx.save();
        this.ctx.translate(this.rocket.x, this.rocket.y);
        this.ctx.rotate(this.rocket.angle);

        // Draw rocket shadow
        this.ctx.save();
        this.ctx.translate(3, 3);
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(-20, -8, 40, 16);
        this.ctx.restore();

        // Draw rocket body with gradient
        const bodyGradient = this.ctx.createLinearGradient(-15, -8, -15, 8);
        bodyGradient.addColorStop(0, '#ff6b6b');
        bodyGradient.addColorStop(0.5, '#ff8e53');
        bodyGradient.addColorStop(1, '#ff6b6b');
        this.ctx.fillStyle = bodyGradient;
        this.ctx.fillRect(-15, -6, 30, 12);

        // Draw rocket details
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(-10, -4, 20, 2);
        this.ctx.fillRect(-10, 2, 20, 2);

        // Draw rocket nose with gradient
        const noseGradient = this.ctx.createLinearGradient(15, -6, 25, 0);
        noseGradient.addColorStop(0, '#ffeb3b');
        noseGradient.addColorStop(1, '#ffc107');
        this.ctx.fillStyle = noseGradient;
        this.ctx.beginPath();
        this.ctx.moveTo(15, 0);
        this.ctx.lineTo(25, -6);
        this.ctx.lineTo(25, 6);
        this.ctx.closePath();
        this.ctx.fill();

        // Draw rocket fins with gradient
        const finGradient = this.ctx.createLinearGradient(-15, -10, -7, -2);
        finGradient.addColorStop(0, '#ee5a24');
        finGradient.addColorStop(1, '#fd79a8');
        this.ctx.fillStyle = finGradient;
        this.ctx.fillRect(-15, -10, 8, 8);
        this.ctx.fillRect(-15, 2, 8, 8);

        // Draw enhanced flame effect
        if (this.gameState === 'flying') {
            const time = Date.now() * 0.01;

            // Main flame
            const flameGradient = this.ctx.createLinearGradient(-30, 0, -15, 0);
            flameGradient.addColorStop(0, '#ffeb3b');
            flameGradient.addColorStop(0.5, '#ff9800');
            flameGradient.addColorStop(1, '#ff5722');

            this.ctx.fillStyle = flameGradient;
            const flameLength = 15 + Math.sin(time) * 5;
            this.ctx.fillRect(-15 - flameLength, -4, flameLength, 8);

            // Inner flame
            this.ctx.fillStyle = '#ffeb3b';
            const innerFlameLength = 8 + Math.sin(time * 1.5) * 3;
            this.ctx.fillRect(-15 - innerFlameLength, -2, innerFlameLength, 4);

            // Flame particles
            for (let i = 0; i < 5; i++) {
                const particleX = -25 - Math.random() * 10;
                const particleY = (Math.random() - 0.5) * 8;
                const particleSize = Math.random() * 2 + 1;

                this.ctx.fillStyle = `rgba(255, ${Math.floor(Math.random() * 100 + 155)}, 0, ${Math.random()})`;
                this.ctx.beginPath();
                this.ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
                this.ctx.fill();
            }
        }

        // Draw rocket glow
        if (this.gameState === 'flying') {
            this.ctx.shadowColor = '#ff6b6b';
            this.ctx.shadowBlur = 20;
            this.ctx.fillStyle = 'rgba(255, 107, 107, 0.1)';
            this.ctx.fillRect(-20, -10, 45, 20);
            this.ctx.shadowBlur = 0;
        }

        this.ctx.restore();
    }

    drawEnhancedGround() {
        const groundHeight = 80;
        const groundY = this.canvas.height - groundHeight;

        // Draw ground gradient
        const groundGradient = this.ctx.createLinearGradient(0, groundY, 0, this.canvas.height);
        groundGradient.addColorStop(0, '#2d3436');
        groundGradient.addColorStop(0.5, '#636e72');
        groundGradient.addColorStop(1, '#2d3436');
        this.ctx.fillStyle = groundGradient;
        this.ctx.fillRect(0, groundY, this.canvas.width, groundHeight);

        // Draw city skyline
        this.drawCitySkyline(groundY);

        // Draw enhanced launch pad
        const padX = 30;
        const padY = groundY - 20;
        const padWidth = 40;
        const padHeight = 20;

        // Launch pad base
        const padGradient = this.ctx.createLinearGradient(padX, padY, padX, padY + padHeight);
        padGradient.addColorStop(0, '#74b9ff');
        padGradient.addColorStop(1, '#0984e3');
        this.ctx.fillStyle = padGradient;
        this.ctx.fillRect(padX, padY, padWidth, padHeight);

        // Launch pad details
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(padX + 5, padY + 5, padWidth - 10, 2);
        this.ctx.fillRect(padX + 5, padY + 10, padWidth - 10, 2);
        this.ctx.fillRect(padX + 5, padY + 15, padWidth - 10, 2);

        // Launch pad lights
        const time = Date.now() * 0.005;
        for (let i = 0; i < 4; i++) {
            const lightX = padX + 8 + i * 8;
            const lightY = padY + 2;
            const brightness = Math.sin(time + i) * 0.5 + 0.5;

            this.ctx.fillStyle = `rgba(0, 255, 0, ${brightness})`;
            this.ctx.beginPath();
            this.ctx.arc(lightX, lightY, 2, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawCitySkyline(groundY) {
        const buildings = [
            { x: 150, width: 60, height: 120 },
            { x: 220, width: 40, height: 80 },
            { x: 270, width: 80, height: 150 },
            { x: 360, width: 50, height: 100 },
            { x: 420, width: 70, height: 130 },
            { x: 500, width: 45, height: 90 },
            { x: 560, width: 90, height: 160 },
            { x: 660, width: 55, height: 110 }
        ];

        buildings.forEach(building => {
            const buildingY = groundY - building.height;

            // Building gradient
            const buildingGradient = this.ctx.createLinearGradient(
                building.x, buildingY,
                building.x, groundY
            );
            buildingGradient.addColorStop(0, '#34495e');
            buildingGradient.addColorStop(1, '#2c3e50');

            this.ctx.fillStyle = buildingGradient;
            this.ctx.fillRect(building.x, buildingY, building.width, building.height);

            // Building windows
            this.ctx.fillStyle = '#f39c12';
            const windowRows = Math.floor(building.height / 15);
            const windowCols = Math.floor(building.width / 12);

            for (let row = 0; row < windowRows; row++) {
                for (let col = 0; col < windowCols; col++) {
                    if (Math.random() > 0.3) { // Random lit windows
                        const windowX = building.x + 4 + col * 12;
                        const windowY = buildingY + 4 + row * 15;
                        this.ctx.fillRect(windowX, windowY, 6, 8);
                    }
                }
            }
        });
    }

    drawParticleEffects() {
        if (this.gameState !== 'flying') return;

        const time = Date.now() * 0.001;

        // Draw floating particles around the rocket
        for (let i = 0; i < 10; i++) {
            const angle = (time + i) * 0.5;
            const radius = 30 + Math.sin(time * 2 + i) * 10;
            const x = this.rocket.x + Math.cos(angle) * radius;
            const y = this.rocket.y + Math.sin(angle) * radius;
            const alpha = Math.sin(time * 3 + i) * 0.3 + 0.2;

            this.ctx.fillStyle = `rgba(255, 235, 59, ${alpha})`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, 2, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawMultiplierGlow() {
        if (this.gameState !== 'flying') return;

        const intensity = Math.min(this.multiplier / 5, 1); // Glow gets stronger with higher multiplier
        const time = Date.now() * 0.005;
        const glowSize = 50 + Math.sin(time) * 10;

        this.ctx.save();
        this.ctx.globalAlpha = intensity * 0.3;

        // Create radial gradient for glow
        const gradient = this.ctx.createRadialGradient(
            this.rocket.x, this.rocket.y, 0,
            this.rocket.x, this.rocket.y, glowSize
        );
        gradient.addColorStop(0, '#ff6b6b');
        gradient.addColorStop(0.5, '#ff8e53');
        gradient.addColorStop(1, 'transparent');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(this.rocket.x, this.rocket.y, glowSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.restore();
    }

    drawMultiplierScale() {
        const scaleX = this.canvas.width - 60;
        const scaleTop = 50;
        const scaleBottom = this.canvas.height - 100;
        const scaleHeight = scaleBottom - scaleTop;

        this.ctx.save();

        // Draw scale background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(scaleX - 5, scaleTop, 50, scaleHeight);

        // Calculate scale values (1x to 10x visible range)
        const minMultiplier = 1.0;
        const maxMultiplier = Math.max(10.0, this.multiplier * 1.5);

        // Draw scale marks and labels
        for (let mult = minMultiplier; mult <= maxMultiplier; mult += 0.1) {
            const y = scaleBottom - ((mult - minMultiplier) / (maxMultiplier - minMultiplier)) * scaleHeight;

            if (y < scaleTop || y > scaleBottom) continue;

            // Draw major marks every 0.5x
            if (mult % 0.5 === 0) {
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.moveTo(scaleX, y);
                this.ctx.lineTo(scaleX + 15, y);
                this.ctx.stroke();

                // Draw labels
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = 'bold 12px Orbitron';
                this.ctx.textAlign = 'right';
                this.ctx.fillText(mult.toFixed(1) + 'x', scaleX - 8, y + 4);
            }
            // Draw minor marks every 0.1x
            else {
                this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                this.ctx.lineWidth = 1;
                this.ctx.beginPath();
                this.ctx.moveTo(scaleX, y);
                this.ctx.lineTo(scaleX + 8, y);
                this.ctx.stroke();
            }
        }

        // Draw current multiplier indicator
        if (this.gameState === 'flying') {
            const currentY = scaleBottom - ((this.multiplier - minMultiplier) / (maxMultiplier - minMultiplier)) * scaleHeight;

            if (currentY >= scaleTop && currentY <= scaleBottom) {
                // Animated indicator
                const time = Date.now() * 0.01;
                const pulseSize = 3 + Math.sin(time) * 2;

                this.ctx.fillStyle = '#ff6b6b';
                this.ctx.beginPath();
                this.ctx.arc(scaleX + 20, currentY, pulseSize, 0, Math.PI * 2);
                this.ctx.fill();

                // Glow effect
                this.ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
                this.ctx.beginPath();
                this.ctx.arc(scaleX + 20, currentY, pulseSize * 2, 0, Math.PI * 2);
                this.ctx.fill();

                // Draw line to current position
                this.ctx.strokeStyle = '#ff6b6b';
                this.ctx.lineWidth = 2;
                this.ctx.setLineDash([5, 5]);
                this.ctx.beginPath();
                this.ctx.moveTo(scaleX + 25, currentY);
                this.ctx.lineTo(this.rocket.x + 30, this.rocket.y);
                this.ctx.stroke();
                this.ctx.setLineDash([]);
            }
        }

        this.ctx.restore();
    }

    drawCenterMultiplier() {
        if (this.gameState !== 'flying') return;

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 3;

        this.ctx.save();

        // Calculate dynamic size based on multiplier
        const baseSize = 60;
        const sizeMultiplier = Math.min(1 + (this.multiplier - 1) * 0.3, 3); // Grows up to 3x size
        const fontSize = baseSize * sizeMultiplier;

        // Pulsing effect
        const time = Date.now() * 0.005;
        const pulse = 1 + Math.sin(time * 2) * 0.1;
        const finalSize = fontSize * pulse;

        // Draw text shadow/glow
        this.ctx.font = `bold ${finalSize}px Orbitron`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // Multiple glow layers for better effect
        const glowColors = [
            { color: 'rgba(255, 107, 107, 0.8)', offset: 0 },
            { color: 'rgba(255, 107, 107, 0.6)', offset: 2 },
            { color: 'rgba(255, 107, 107, 0.4)', offset: 4 },
            { color: 'rgba(255, 107, 107, 0.2)', offset: 8 }
        ];

        glowColors.forEach(glow => {
            this.ctx.fillStyle = glow.color;
            for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 4) {
                const offsetX = Math.cos(angle) * glow.offset;
                const offsetY = Math.sin(angle) * glow.offset;
                this.ctx.fillText(this.multiplier.toFixed(2) + 'x', centerX + offsetX, centerY + offsetY);
            }
        });

        // Draw main text
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillText(this.multiplier.toFixed(2) + 'x', centerX, centerY);

        // Add sparkle effects around high multipliers
        if (this.multiplier > 2.0) {
            for (let i = 0; i < 8; i++) {
                const angle = (time + i) * 0.5;
                const radius = 80 + Math.sin(time * 3 + i) * 20;
                const sparkleX = centerX + Math.cos(angle) * radius;
                const sparkleY = centerY + Math.sin(angle) * radius;
                const sparkleSize = Math.random() * 3 + 1;

                this.ctx.fillStyle = `rgba(255, 255, 255, ${Math.random() * 0.8 + 0.2})`;
                this.ctx.beginPath();
                this.ctx.arc(sparkleX, sparkleY, sparkleSize, 0, Math.PI * 2);
                this.ctx.fill();
            }
        }

        this.ctx.restore();
    }

    drawTimeScale() {
        if (this.gameState !== 'flying') return;

        const currentTime = (Date.now() - this.startTime) / 1000; // Current time in seconds
        const scaleY = this.canvas.height - 30;
        const scaleLeft = 50;
        const scaleRight = this.canvas.width - 100;
        const scaleWidth = scaleRight - scaleLeft;

        this.ctx.save();

        // Draw time scale background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(scaleLeft - 5, scaleY - 15, scaleWidth + 10, 30);

        // Calculate time range (show current time ± some buffer)
        const timeRange = Math.max(10, currentTime + 2); // At least 10 seconds visible
        const timeStep = timeRange > 20 ? 2 : 1; // 2s steps for longer ranges, 1s for shorter

        // Draw time marks
        for (let t = 0; t <= timeRange; t += timeStep) {
            const x = scaleLeft + (t / timeRange) * scaleWidth;

            if (x > scaleRight) break;

            // Draw tick marks
            this.ctx.strokeStyle = t % (timeStep * 2) === 0 ? '#ffffff' : 'rgba(255, 255, 255, 0.5)';
            this.ctx.lineWidth = t % (timeStep * 2) === 0 ? 2 : 1;
            this.ctx.beginPath();
            this.ctx.moveTo(x, scaleY - 8);
            this.ctx.lineTo(x, scaleY + 8);
            this.ctx.stroke();

            // Draw time labels for major marks
            if (t % (timeStep * 2) === 0) {
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = '12px Orbitron';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(t.toFixed(1) + 's', x, scaleY + 25);
            }
        }

        // Draw current time indicator
        const currentX = scaleLeft + (currentTime / timeRange) * scaleWidth;
        if (currentX <= scaleRight) {
            const time = Date.now() * 0.01;
            const pulseSize = 3 + Math.sin(time) * 1;

            // Current time marker
            this.ctx.fillStyle = '#ff6b6b';
            this.ctx.beginPath();
            this.ctx.arc(currentX, scaleY, pulseSize, 0, Math.PI * 2);
            this.ctx.fill();

            // Glow effect
            this.ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
            this.ctx.beginPath();
            this.ctx.arc(currentX, scaleY, pulseSize * 2, 0, Math.PI * 2);
            this.ctx.fill();

            // Vertical line to rocket
            this.ctx.strokeStyle = 'rgba(255, 107, 107, 0.5)';
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([3, 3]);
            this.ctx.beginPath();
            this.ctx.moveTo(currentX, scaleY - 15);
            this.ctx.lineTo(this.rocket.x, this.rocket.y + 20);
            this.ctx.stroke();
            this.ctx.setLineDash([]);
        }

        this.ctx.restore();
    }

    renderCrash() {
        // Enhanced explosion effect
        this.ctx.save();
        this.ctx.translate(this.rocket.x, this.rocket.y);

        const time = Date.now() * 0.01;

        // Draw multiple explosion layers
        for (let i = 0; i < 8; i++) {
            const radius = 15 + i * 12 + Math.sin(time + i) * 5;
            const alpha = 0.9 - i * 0.1;

            // Explosion colors
            const colors = ['#ff6b6b', '#ff8e53', '#ffeb3b', '#ff5722'];
            const color = colors[i % colors.length];

            this.ctx.beginPath();
            this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
            this.ctx.fillStyle = color.replace(')', `, ${alpha})`).replace('rgb', 'rgba');
            this.ctx.fill();
        }

        // Draw explosion particles
        for (let i = 0; i < 20; i++) {
            const angle = (i / 20) * Math.PI * 2;
            const distance = 30 + Math.random() * 40;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;
            const size = Math.random() * 4 + 2;

            this.ctx.fillStyle = `rgba(255, ${Math.floor(Math.random() * 100 + 155)}, 0, ${Math.random()})`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }

        this.ctx.restore();
    }
}

// Initialize the game when the page loads
let rocketGame;

// Function to initialize the game
function initializeRocketGame() {
    if (!rocketGame) {
        console.log('Initializing Rocket Game...');
        rocketGame = new RocketGame();
        console.log('Rocket Game initialized successfully');
    }
}

// Function to check if user is logged in and start game
function checkUserAndStartGame() {
    const userInfo = document.getElementById('userInfo');
    if (userInfo && userInfo.style.display !== 'none') {
        initializeRocketGame();
        return true;
    }
    return false;
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for user...');

    // Try to start immediately if user is already logged in
    if (!checkUserAndStartGame()) {
        // If not logged in, wait for login
        const startGameWhenReady = () => {
            if (checkUserAndStartGame()) {
                console.log('User logged in, game started');
                return;
            }
            setTimeout(startGameWhenReady, 1000);
        };

        // Start checking after a short delay
        setTimeout(startGameWhenReady, 500);
    }
});

// Make the initialization function globally available
window.initializeRocketGame = initializeRocketGame;
