#!/usr/bin/env python3
"""
Simple Python HTTP Server for Casino Template
Serves static files and provides basic web server functionality
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from pathlib import Path

class CasinoHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for the casino template"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Add cache control for development
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        super().end_headers()
    
    def do_GET(self):
        # Serve index.html for root path
        if self.path == '/':
            self.path = '/index.html'
        
        # Handle file extensions for proper MIME types
        if self.path.endswith('.js'):
            self.send_response(200)
            self.send_header('Content-type', 'application/javascript')
            self.end_headers()
            try:
                with open(self.path[1:], 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404)
            return
        
        if self.path.endswith('.css'):
            self.send_response(200)
            self.send_header('Content-type', 'text/css')
            self.end_headers()
            try:
                with open(self.path[1:], 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404)
            return
        
        # Default handling for other files
        super().do_GET()
    
    def log_message(self, format, *args):
        """Custom log message format"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def find_free_port(host='*************', start_port=8000, max_attempts=100):
    """Find a free port starting from start_port on the specified host"""
    import socket

    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind((host, port))
                return port
        except OSError:
            continue

    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts} on {host}")

def get_local_ip():
    """Get the local IP address"""
    import socket
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"

def main():
    """Main server function"""
    # Change to the directory containing this script
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)

    # Check if index.html exists
    if not os.path.exists('index.html'):
        print("❌ Error: index.html not found in current directory!")
        print(f"Current directory: {os.getcwd()}")
        print("Make sure you're running this from the casino-template directory.")
        sys.exit(1)

    # Set the host IP address (can be overridden by command line argument)
    if len(sys.argv) > 1:
        host_ip = sys.argv[1]
        print(f"🔧 Using IP from command line: {host_ip}")
    else:
        host_ip = "*************"  # Your specified local IP

    detected_ip = get_local_ip()

    # Find a free port
    try:
        port = find_free_port(host_ip)
    except RuntimeError as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

    # Create and start the server
    try:
        with socketserver.TCPServer((host_ip, port), CasinoHTTPRequestHandler) as httpd:
            local_url = f"http://{host_ip}:{port}"
            localhost_url = f"http://localhost:{port}"

            print("🎰 Casino Template Server Starting...")
            print("=" * 60)
            print(f"🌐 Network URL: {local_url}")
            print(f"🏠 Local URL:   {localhost_url}")
            print(f"📡 Host IP:     {host_ip}")
            print(f"🔍 Detected IP: {detected_ip}")
            print(f"📁 Serving from: {os.getcwd()}")
            print(f"🚀 Port: {port}")
            print("=" * 60)
            print("📱 Access from other devices on your network:")
            print(f"   {local_url}")
            print("=" * 60)
            print("📝 Server Logs:")
            print("Press Ctrl+C to stop the server")
            print()

            # Try to open browser automatically with localhost
            try:
                webbrowser.open(localhost_url)
                print(f"🌐 Opened {localhost_url} in your default browser")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"Please manually open: {localhost_url}")

            print()
            print("💡 Tips:")
            print(f"   • Use {localhost_url} on this computer")
            print(f"   • Use {local_url} from other devices")
            print("   • Make sure your firewall allows connections on this port")
            print()

            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
