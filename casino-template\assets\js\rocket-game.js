// Modern Rocket Game with Curved Trajectory
class RocketGameEngine {
    constructor() {
        this.canvas = document.getElementById('rocketCanvas');
        this.ctx = this.canvas ? this.canvas.getContext('2d') : null;
        this.gameState = 'waiting'; // waiting, countdown, flying, crashed
        this.multiplier = 1.00;
        this.crashPoint = 0;
        this.startTime = 0;
        this.currentBet = 0;
        this.hasActiveBet = false;
        this.hasCashedOut = false;
        this.gameHistory = [];
        this.animationId = null;
        this.gameTime = 0;
        this.autoBetEnabled = false;

        // Rocket properties with curved trajectory
        this.rocket = {
            x: 100,
            y: 0,
            baseX: 100,
            baseY: 0,
            angle: 0,
            trail: [],
            speed: 2,
            size: 40
        };

        // Camera properties for dynamic expansion
        this.camera = {
            x: 0,
            y: 0,
            scale: 1,
            targetScale: 1,
            smoothing: 0.05,
            maxScale: 3
        };

        // Trajectory curve parameters
        this.trajectory = {
            startX: 100,
            startY: 0,
            controlX: 400,
            controlY: -200,
            endX: 800,
            endY: -400,
            progress: 0
        };

        // Real logged-in players and their bets
        this.activePlayers = [];
        this.cashedOutPlayers = [];
        this.totalPlayers = 0;

        // Initialize real player system
        this.initializeRealPlayers();

        // Game settings
        this.countdownTime = 5000;
        this.minCrashPoint = 1.01;
        this.maxCrashPoint = 100.0;

        if (this.canvas) {
            this.initializeCanvas();
            this.bindEvents();
            this.startGameLoop();
        }
    }
    
    initializeCanvas() {
        this.canvas.width = 1000;
        this.canvas.height = 600;

        // Set trajectory based on canvas size
        this.trajectory.startX = this.canvas.width * 0.1;
        this.trajectory.startY = this.canvas.height * 0.85;
        this.trajectory.controlX = this.canvas.width * 0.5;
        this.trajectory.controlY = this.canvas.height * 0.3;
        this.trajectory.endX = this.canvas.width * 0.9;
        this.trajectory.endY = this.canvas.height * 0.1;

        // Initialize rocket position
        this.rocket.x = this.trajectory.startX;
        this.rocket.y = this.trajectory.startY;
        this.rocket.baseX = this.trajectory.startX;
        this.rocket.baseY = this.trajectory.startY;

        // Initialize stars for background
        this.stars = [];
        for (let i = 0; i < 150; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width * 2,
                y: Math.random() * this.canvas.height * 2,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.6 + 0.2,
                twinkle: Math.random() * Math.PI * 2
            });
        }

        // Initialize player list in UI
        this.updatePlayersList();
    }
    
    bindEvents() {
        const placeBetBtn = document.getElementById('placeBetBtn');
        const cashOutBtn = document.getElementById('cashOutBtn');
        const betAmountInput = document.getElementById('betAmount');
        const quickBetBtns = document.querySelectorAll('.quick-bet-btn');
        const autoBetToggle = document.getElementById('autoBet');

        if (placeBetBtn) {
            placeBetBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Bet button clicked'); // Debug log
                this.placeBet();
            });
        }

        if (cashOutBtn) {
            cashOutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Cash out button clicked'); // Debug log
                this.cashOut();
            });
        }

        if (betAmountInput) {
            betAmountInput.addEventListener('input', () => this.updatePotentialWin());
        }

        // Auto bet toggle
        if (autoBetToggle) {
            autoBetToggle.addEventListener('change', (e) => {
                this.autoBetEnabled = e.target.checked;
                console.log('Auto bet:', this.autoBetEnabled); // Debug log
            });
        }

        // Quick bet buttons
        quickBetBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const multiplier = parseFloat(e.target.dataset.multiplier);
                const action = e.target.dataset.action;

                if (action === 'max') {
                    betAmountInput.value = 1000;
                } else if (multiplier) {
                    const currentBet = parseFloat(betAmountInput.value) || 25;
                    betAmountInput.value = Math.min(1000, currentBet * multiplier);
                }
                this.updatePotentialWin();
            });
        });
    }

    // Calculate point on quadratic Bezier curve
    getPointOnCurve(t) {
        const { startX, startY, controlX, controlY, endX, endY } = this.trajectory;

        const x = Math.pow(1 - t, 2) * startX +
                  2 * (1 - t) * t * controlX +
                  Math.pow(t, 2) * endX;

        const y = Math.pow(1 - t, 2) * startY +
                  2 * (1 - t) * t * controlY +
                  Math.pow(t, 2) * endY;

        return { x, y };
    }

    // Calculate curve tangent for rocket angle
    getCurveTangent(t) {
        const { startX, startY, controlX, controlY, endX, endY } = this.trajectory;

        const dx = 2 * (1 - t) * (controlX - startX) + 2 * t * (endX - controlX);
        const dy = 2 * (1 - t) * (controlY - startY) + 2 * t * (endY - controlY);

        return Math.atan2(dy, dx);
    }

    initializeRealPlayers() {
        // Initialize with only real logged-in users
        this.activePlayers = [];

        // Add current user if they have an active bet
        const currentUser = this.getCurrentUser();
        if (currentUser && this.hasActiveBet) {
            this.activePlayers.push({
                id: currentUser.id || 'current',
                username: currentUser.username || currentUser.email || 'You',
                bet: this.currentBet,
                cashedOut: this.hasCashedOut,
                cashOutMultiplier: null,
                cashOutPosition: null,
                isCurrentUser: true
            });
        }

        // In a real application, this would fetch other active players from the server
        // For now, we show NO simulated users - only real ones
        this.simulateOnlineUsers();

        this.updatePlayersList();
        this.updatePlayersCount();
    }

    getCurrentUser() {
        // Try to get current user from various sources
        if (window.userManager && window.userManager.getCurrentUser) {
            return window.userManager.getCurrentUser();
        }

        // Check localStorage for user data
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            try {
                return JSON.parse(userData);
            } catch (e) {
                console.log('Error parsing user data:', e);
            }
        }

        // Check if user section is visible (user is logged in)
        const userSection = document.getElementById('userSection');
        if (userSection && !userSection.classList.contains('d-none')) {
            const usernameElement = document.querySelector('#userSection .username');
            if (usernameElement) {
                return {
                    username: usernameElement.textContent,
                    id: 'current'
                };
            }
        }

        return null;
    }

    simulateOnlineUsers() {
        // NO SIMULATION - Only real users will appear
        // In a real application, this would fetch actual online users from the server
        // For now, we only show the current user when they place a bet

        this.totalPlayers = this.activePlayers.length;
    }

    updatePlayersList() {
        const playersList = document.getElementById('playersList');
        if (!playersList) return;

        // Filter out current user for the "other players" display
        const otherPlayers = this.activePlayers.filter(player => !player.isCurrentUser);
        const currentUserPlayer = this.activePlayers.find(player => player.isCurrentUser);

        let html = '';

        // Show current user first if they have a bet
        if (currentUserPlayer) {
            html += `
                <div class="player-item ${currentUserPlayer.cashedOut ? 'cashed-out' : ''} current-user">
                    <div class="player-info">
                        <div class="player-avatar current-user-avatar">YOU</div>
                        <span class="player-name">You</span>
                    </div>
                    <div class="player-amount">
                        ${currentUserPlayer.cashedOut ?
                            `<span class="cash-out-amount">$${(currentUserPlayer.bet * currentUserPlayer.cashOutMultiplier).toFixed(2)}</span>` :
                            `<i class="fas fa-coins"></i> ${currentUserPlayer.bet.toFixed(2)}`
                        }
                    </div>
                </div>
            `;
        }

        // Show other real players (none for now since we removed simulation)
        if (otherPlayers.length === 0 && !currentUserPlayer) {
            html = '<div class="no-players">No players in this round</div>';
        } else if (otherPlayers.length === 0 && currentUserPlayer) {
            // Don't show "no other players" message, just show the current user
        } else {
            // This would show other real players when they exist
            otherPlayers.forEach(player => {
                const avatar = player.username.substring(0, 2).toUpperCase();
                html += `
                    <div class="player-item ${player.cashedOut ? 'cashed-out' : ''}">
                        <div class="player-info">
                            <div class="player-avatar">${avatar}</div>
                            <span class="player-name">${player.username}</span>
                        </div>
                        <div class="player-amount">
                            ${player.cashedOut ?
                                `<span class="cash-out-amount">$${(player.bet * player.cashOutMultiplier).toFixed(2)}</span>` :
                                `<i class="fas fa-coins"></i> ${player.bet.toFixed(2)}`
                            }
                        </div>
                    </div>
                `;
            });
        }

        playersList.innerHTML = html;
    }

    updatePlayersCount() {
        const playersCountElement = document.getElementById('playersCount');
        if (playersCountElement) {
            playersCountElement.textContent = this.totalPlayers;
        }
    }

    addCurrentUserToActivePlayers() {
        const currentUser = this.getCurrentUser();
        if (!currentUser) return;

        // Remove existing current user entry if any
        this.activePlayers = this.activePlayers.filter(player => !player.isCurrentUser);

        // Add current user with their bet
        this.activePlayers.unshift({
            id: currentUser.id || 'current',
            username: currentUser.username || currentUser.email || 'You',
            bet: this.currentBet,
            cashedOut: false,
            cashOutMultiplier: null,
            cashOutPosition: null,
            isCurrentUser: true
        });

        this.totalPlayers = this.activePlayers.length;
        this.updatePlayersList();
        this.updatePlayersCount();
    }

    removeCurrentUserFromActivePlayers() {
        this.activePlayers = this.activePlayers.filter(player => !player.isCurrentUser);
        this.totalPlayers = this.activePlayers.length;
        this.updatePlayersList();
        this.updatePlayersCount();
    }

    updateCurrentUserCashOut() {
        const currentUserPlayer = this.activePlayers.find(player => player.isCurrentUser);
        if (currentUserPlayer) {
            currentUserPlayer.cashedOut = true;
            currentUserPlayer.cashOutMultiplier = this.multiplier;
            currentUserPlayer.cashOutPosition = this.trajectory.progress;

            // Add to cashed out players for trajectory display
            this.cashedOutPlayers.push({
                ...currentUserPlayer,
                position: this.trajectory.progress
            });

            this.updatePlayersList();
        }
    }
    
    generateCrashPoint() {
        const random = Math.random();
        const crashPoint = Math.max(
            this.minCrashPoint,
            Math.min(this.maxCrashPoint, 1 / (1 - random * 0.99))
        );
        return Math.round(crashPoint * 100) / 100;
    }
    
    placeBet() {
        console.log('placeBet called, gameState:', this.gameState); // Debug log

        if (this.gameState !== 'waiting') {
            console.log('Cannot bet, game state is:', this.gameState);
            this.showNotification('Cannot place bet right now!', 'error');
            return;
        }

        const betAmountInput = document.getElementById('betAmount');
        if (!betAmountInput) {
            console.log('Bet amount input not found');
            return;
        }

        const betAmount = parseFloat(betAmountInput.value);
        const balanceElement = document.getElementById('userBalance');
        const userBalance = balanceElement ?
            parseFloat(balanceElement.textContent) || 1000 :
            (this.virtualBalance || 1000);

        console.log('Bet amount:', betAmount, 'User balance:', userBalance);

        if (isNaN(betAmount) || betAmount <= 0) {
            this.showNotification('Please enter a valid bet amount!', 'error');
            return;
        }

        if (betAmount > userBalance) {
            this.showNotification('Insufficient balance!', 'error');
            return;
        }

        this.currentBet = betAmount;
        this.hasActiveBet = true;
        this.hasCashedOut = false;

        console.log('Bet placed successfully:', betAmount);

        // Add current user to active players
        this.addCurrentUserToActivePlayers();

        // Update balance
        this.updateBalance(-betAmount);

        // Update UI - hide bet button, show cash out button
        const placeBetBtn = document.getElementById('placeBetBtn');
        const cashOutBtn = document.getElementById('cashOutBtn');

        if (placeBetBtn) {
            placeBetBtn.classList.add('d-none');
            placeBetBtn.disabled = true;
        }

        if (cashOutBtn) {
            cashOutBtn.classList.remove('d-none');
            cashOutBtn.disabled = false;
        }

        this.updateGameStatus('Bet placed! Round starting soon...');
        this.showNotification(`Bet of $${betAmount.toFixed(2)} placed successfully!`, 'success');

        // Start the game if not already started
        if (this.gameState === 'waiting') {
            setTimeout(() => this.startRound(), 2000);
        }
    }
    
    cashOut() {
        if (!this.hasActiveBet || this.hasCashedOut || this.gameState !== 'flying') return;

        this.hasCashedOut = true;
        const winAmount = this.currentBet * this.multiplier;

        // Update current user's cash out status
        this.updateCurrentUserCashOut();

        // Update balance
        this.updateBalance(winAmount);

        // Update UI - hide cash out button and show bet button
        const cashOutBtn = document.getElementById('cashOutBtn');
        const placeBetBtn = document.getElementById('placeBetBtn');

        if (cashOutBtn) {
            cashOutBtn.classList.add('d-none');
            cashOutBtn.disabled = true;
        }

        if (placeBetBtn) {
            placeBetBtn.classList.remove('d-none');
            placeBetBtn.disabled = false;
        }

        // Update stats
        this.updateStats(this.currentBet, winAmount);

        this.updateGameStatus(`Cashed out at ${this.multiplier.toFixed(2)}x! Won $${winAmount.toFixed(2)}`);
        this.showNotification(`Successfully cashed out $${winAmount.toFixed(2)}!`, 'success');
    }
    
    updateBalance(amount) {
        const balanceElement = document.getElementById('userBalance');
        if (!balanceElement) {
            console.log('Balance element not found, creating virtual balance');
            // Create virtual balance if element doesn't exist
            if (!this.virtualBalance) {
                this.virtualBalance = 1000; // Starting balance
            }
            this.virtualBalance = Math.max(0, this.virtualBalance + amount);
            console.log('Virtual balance updated to:', this.virtualBalance);
            return;
        }

        const currentBalance = parseFloat(balanceElement.textContent) || 1000;
        const newBalance = Math.max(0, currentBalance + amount);
        balanceElement.textContent = newBalance.toFixed(2);

        console.log('Balance updated:', currentBalance, '+', amount, '=', newBalance);

        // Update user data in localStorage
        if (window.userManager && window.userManager.getCurrentUser()) {
            window.userManager.updateUserBalance(newBalance);
        }
    }
    
    updatePotentialWin() {
        const betAmount = parseFloat(document.getElementById('betAmount').value) || 0;
        const potentialWin = betAmount * this.multiplier;
        const potentialWinElement = document.getElementById('potentialWin');
        if (potentialWinElement) {
            potentialWinElement.textContent = potentialWin.toFixed(2);
        }
    }
    
    updateGameStatus(message) {
        const statusElement = document.getElementById('gameStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }
    
    updateMultiplierDisplay() {
        const multiplierElement = document.getElementById('currentMultiplier');
        if (multiplierElement) {
            multiplierElement.textContent = this.multiplier.toFixed(2) + 'x';
            
            // Add dynamic scaling based on multiplier
            const scale = Math.min(1 + (this.multiplier - 1) * 0.1, 2);
            multiplierElement.style.transform = `translate(-50%, -50%) scale(${scale})`;
        }
        this.updatePotentialWin();
    }
    
    updateStats(wagered, won) {
        // Update games played
        const gamesPlayedElement = document.getElementById('gamesPlayed');
        if (gamesPlayedElement) {
            const gamesPlayed = parseInt(gamesPlayedElement.textContent) + 1;
            gamesPlayedElement.textContent = gamesPlayed;
        }
        
        // Update total wagered
        const totalWageredElement = document.getElementById('totalWagered');
        if (totalWageredElement) {
            const totalWagered = parseFloat(totalWageredElement.textContent) + wagered;
            totalWageredElement.textContent = totalWagered.toFixed(2);
        }
        
        // Update total won
        const totalWonElement = document.getElementById('totalWon');
        if (totalWonElement) {
            const totalWon = parseFloat(totalWonElement.textContent) + won;
            totalWonElement.textContent = totalWon.toFixed(2);
        }
    }
    
    addToHistory(crashPoint) {
        this.gameHistory.unshift(crashPoint);
        if (this.gameHistory.length > 10) {
            this.gameHistory.pop();
        }
        this.updateHistoryDisplay();
    }
    
    updateHistoryDisplay() {
        const historyContainer = document.getElementById('gameHistory');
        if (!historyContainer) return;
        
        historyContainer.innerHTML = '';
        
        this.gameHistory.forEach(crashPoint => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            let crashClass = 'low';
            if (crashPoint >= 2.0) crashClass = 'medium';
            if (crashPoint >= 5.0) crashClass = 'high';
            
            historyItem.innerHTML = `
                <span>Crashed at</span>
                <span class="crash-multiplier ${crashClass}">${crashPoint.toFixed(2)}x</span>
            `;
            
            historyContainer.appendChild(historyItem);
        });
    }
    
    startNewRound() {
        // Reset game state
        this.gameState = 'waiting';
        this.multiplier = 1.00;
        this.crashPoint = this.generateCrashPoint();
        this.hasActiveBet = false;
        this.hasCashedOut = false;
        this.currentBet = 0;

        // Reset rocket position
        this.rocket.x = this.canvas.width * 0.2;
        this.rocket.y = this.rocket.baseY;
        this.rocket.angle = 0;
        this.rocket.trail = [];
        this.rocket.targetX = this.rocket.x;
        this.rocket.targetY = this.rocket.y;

        // Reset camera
        this.camera.x = 0;
        this.camera.y = 0;
        this.camera.targetX = 0;
        this.camera.targetY = 0;

        // Reset UI
        const placeBetBtn = document.getElementById('placeBetBtn');
        const cashOutBtn = document.getElementById('cashOutBtn');

        if (placeBetBtn) placeBetBtn.disabled = false;
        if (cashOutBtn) cashOutBtn.disabled = true;

        this.updateMultiplierDisplay();
        this.updateGameStatus('Place your bet for the next round!');

        // Start countdown after a short delay
        setTimeout(() => {
            this.startCountdown();
        }, 3000);
    }
    
    startCountdown() {
        this.gameState = 'countdown';
        let countdown = 5;
        
        const countdownInterval = setInterval(() => {
            this.updateGameStatus(`Round starting in ${countdown}...`);
            countdown--;
            
            if (countdown < 0) {
                clearInterval(countdownInterval);
                this.startFlight();
            }
        }, 1000);
    }
    
    startFlight() {
        this.gameState = 'flying';
        this.startTime = Date.now();
        this.updateGameStatus('Rocket is flying! Cash out before it crashes!');
        
        // Disable bet placement
        const placeBetBtn = document.getElementById('placeBetBtn');
        if (placeBetBtn) placeBetBtn.disabled = true;
        
        this.gameLoop();
    }
    
    gameLoop() {
        if (this.gameState !== 'flying') return;

        const currentTime = Date.now();
        this.gameTime = (currentTime - this.startTime) / 1000;

        // Calculate multiplier with exponential growth
        this.multiplier = Math.pow(1.1, this.gameTime * 2);

        // Check if we've reached the crash point
        if (this.multiplier >= this.crashPoint) {
            this.crash();
            return;
        }

        // Update rocket position along curve
        this.updateRocketTrajectory();
        this.updateCamera();

        // Update UI
        this.updateMultiplierDisplay();
        this.updateCashOutButton();

        // Render the game
        this.render();

        // Continue the game loop
        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }

    updateRocketTrajectory() {
        // Calculate progress along curve (0 to 1)
        const progress = Math.min(this.gameTime / 15, 1); // 15 seconds to complete curve
        this.trajectory.progress = progress;

        // Get position on curve
        const position = this.getPointOnCurve(progress);
        this.rocket.x = position.x;
        this.rocket.y = position.y;

        // Calculate rocket angle based on curve tangent
        this.rocket.angle = this.getCurveTangent(progress);

        // Add rocket to trail
        this.rocket.trail.push({ x: this.rocket.x, y: this.rocket.y, time: Date.now() });

        // Limit trail length
        if (this.rocket.trail.length > 50) {
            this.rocket.trail.shift();
        }

        // Handle AI player cash outs
        this.handleAIPlayerCashOuts();

        // Controlled camera scaling - prevent excessive zoom
        const maxProgress = 0.7; // Limit how far the rocket can go
        if (progress > maxProgress) {
            // Stop the rocket progression and maintain current scale
            this.trajectory.progress = maxProgress;
            this.camera.targetScale = Math.min(2, this.camera.targetScale);
        } else if (this.rocket.x > this.canvas.width * 0.6 || this.rocket.y < this.canvas.height * 0.4) {
            this.camera.targetScale = Math.min(2, 1 + progress * 1.5);
        }
    }

    handleAIPlayerCashOuts() {
        // NO AI SIMULATION - Only real user cash outs will be handled
        // In a real application, this would receive real-time updates from the server
        // about other players cashing out

        // This method is kept for future real multiplayer implementation
        // but currently does nothing to avoid fake players
    }

    updateCashOutButton() {
        const cashOutBtn = document.getElementById('cashOutBtn');
        const cashOutAmount = document.getElementById('cashOutAmount');

        if (this.hasActiveBet && !this.hasCashedOut && cashOutBtn && cashOutAmount) {
            const winAmount = this.currentBet * this.multiplier;
            cashOutAmount.textContent = winAmount.toFixed(2);

            // Show cash out button
            cashOutBtn.classList.remove('d-none');
            document.getElementById('placeBetBtn').classList.add('d-none');
        }
    }

    updateCamera() {
        // Follow rocket with camera
        this.camera.x += (this.rocket.x - this.canvas.width / 2 - this.camera.x) * this.camera.smoothing;
        this.camera.y += (this.rocket.y - this.canvas.height / 2 - this.camera.y) * this.camera.smoothing;

        // Update camera scale for dynamic expansion
        this.camera.scale += (this.camera.targetScale - this.camera.scale) * this.camera.smoothing;

        // Ensure camera doesn't go too far
        this.camera.x = Math.max(-this.canvas.width, Math.min(this.canvas.width * 2, this.camera.x));
        this.camera.y = Math.max(-this.canvas.height, Math.min(this.canvas.height * 2, this.camera.y));
    }

    startGameLoop() {
        // Start the render loop
        this.render();
        requestAnimationFrame(() => this.startGameLoop());
    }

    startRound() {
        if (this.gameState !== 'waiting') return;

        // Generate new crash point
        this.crashPoint = this.generateCrashPoint();

        // Reset game state
        this.gameState = 'flying';
        this.gameTime = 0;
        this.multiplier = 1.00;
        this.startTime = Date.now();

        // Reset rocket position
        this.rocket.x = this.trajectory.startX;
        this.rocket.y = this.trajectory.startY;
        this.rocket.trail = [];
        this.trajectory.progress = 0;

        // Reset camera
        this.camera.x = 0;
        this.camera.y = 0;
        this.camera.scale = 1;
        this.camera.targetScale = 1;

        // Reset players
        this.cashedOutPlayers = [];
        this.initializeRealPlayers();

        // Update UI
        this.updateGameStatus('Rocket is flying! Cash out before it crashes!');

        // Start game loop
        this.gameLoop();
    }

    startNewRound() {
        console.log('Starting new round');

        // Reset for new round
        this.gameState = 'waiting';
        this.hasActiveBet = false;
        this.hasCashedOut = false;
        this.currentBet = 0;

        // Remove current user from active players if they're not betting again
        this.removeCurrentUserFromActivePlayers();

        // Reset UI
        const placeBetBtn = document.getElementById('placeBetBtn');
        const cashOutBtn = document.getElementById('cashOutBtn');

        if (placeBetBtn) {
            placeBetBtn.classList.remove('d-none');
            placeBetBtn.disabled = false;
        }

        if (cashOutBtn) {
            cashOutBtn.classList.add('d-none');
            cashOutBtn.disabled = true;
        }

        this.updateGameStatus('Place your bet for the next round!');

        // Handle auto bet
        if (this.autoBetEnabled) {
            setTimeout(() => {
                if (this.gameState === 'waiting' && this.autoBetEnabled) {
                    console.log('Auto betting...');
                    this.placeBet();
                }
            }, 1000);
        }

        // Auto start round after delay
        setTimeout(() => {
            if (this.gameState === 'waiting') {
                this.startRound();
            }
        }, 5000);
    }
    
    updateRocket(elapsedTime) {
        // Calculate rocket movement - keep it more centered
        const progress = Math.min(elapsedTime / 10, 1); // Normalize over 10 seconds

        // Rocket follows a curved path but stays more visible
        this.rocket.targetX = this.canvas.width * 0.2 + (progress * this.canvas.width * 0.3);
        this.rocket.targetY = this.rocket.baseY - (progress * this.canvas.height * 0.6);

        // Smooth movement
        this.rocket.x += (this.rocket.targetX - this.rocket.x) * 0.1;
        this.rocket.y += (this.rocket.targetY - this.rocket.y) * 0.1;

        // Add slight oscillation for realism
        this.rocket.angle = Math.sin(elapsedTime * 3) * 0.1;

        // Update camera to follow rocket smoothly - keep rocket more centered
        this.camera.targetX = Math.max(0, this.rocket.x - this.canvas.width * 0.5);
        this.camera.targetY = Math.max(0, this.rocket.y - this.canvas.height * 0.5);

        this.camera.x += (this.camera.targetX - this.camera.x) * this.camera.smoothing;
        this.camera.y += (this.camera.targetY - this.camera.y) * this.camera.smoothing;

        // Add to trail
        this.rocket.trail.push({
            x: this.rocket.x,
            y: this.rocket.y,
            time: elapsedTime
        });

        // Limit trail length
        if (this.rocket.trail.length > 50) {
            this.rocket.trail.shift();
        }
    }
    
    crash() {
        this.gameState = 'crashed';
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // Handle active bets
        if (this.hasActiveBet && !this.hasCashedOut) {
            this.updateStats(this.currentBet, 0);
            this.updateGameStatus(`Rocket crashed at ${this.crashPoint.toFixed(2)}x! You lost $${this.currentBet.toFixed(2)}`);
            this.showNotification(`Rocket crashed! Lost $${this.currentBet.toFixed(2)}`, 'error');
        } else {
            this.updateGameStatus(`Rocket crashed at ${this.crashPoint.toFixed(2)}x!`);
        }
        
        // Add to history
        this.addToHistory(this.crashPoint);
        
        // Render crash effect
        this.renderCrash();
        
        // Start new round after delay
        setTimeout(() => {
            this.startNewRound();
        }, 5000);
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    render() {
        if (!this.ctx) return;

        // Clear canvas with space background
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#1e2a5e');
        gradient.addColorStop(0.5, '#2d3561');
        gradient.addColorStop(1, '#1e2a5e');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Save context for camera transform
        this.ctx.save();

        // Apply camera transform with scaling
        this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);
        this.ctx.scale(this.camera.scale, this.camera.scale);
        this.ctx.translate(-this.canvas.width / 2 - this.camera.x, -this.canvas.height / 2 - this.camera.y);

        // Draw space background with stars
        this.drawSpaceBackground();

        // Draw trajectory curve
        this.drawTrajectoryPath();

        // Draw player avatars along trajectory
        this.drawPlayersOnTrajectory();

        // Draw rocket trail
        this.drawModernRocketTrail();

        // Draw rocket
        this.drawModernRocket();

        // Restore context
        this.ctx.restore();

        // Draw UI elements (not affected by camera)
        this.drawGameUI();
    }

    drawSpaceBackground() {
        // Draw twinkling stars
        const time = Date.now() * 0.001;

        this.stars.forEach(star => {
            const twinkle = Math.sin(time + star.twinkle) * 0.3 + 0.7;
            this.ctx.globalAlpha = star.opacity * twinkle;
            this.ctx.fillStyle = '#ffffff';
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            this.ctx.fill();
        });

        this.ctx.globalAlpha = 1;
    }

    drawTrajectoryPath() {
        // Draw the curved trajectory path
        this.ctx.strokeStyle = 'rgba(66, 133, 244, 0.3)';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([10, 10]);
        this.ctx.beginPath();

        // Draw curve using quadratic bezier
        this.ctx.moveTo(this.trajectory.startX, this.trajectory.startY);
        this.ctx.quadraticCurveTo(
            this.trajectory.controlX,
            this.trajectory.controlY,
            this.trajectory.endX,
            this.trajectory.endY
        );
        this.ctx.stroke();
        this.ctx.setLineDash([]);

        // Draw progress line (completed path)
        if (this.trajectory.progress > 0) {
            this.ctx.strokeStyle = 'rgba(66, 133, 244, 0.8)';
            this.ctx.lineWidth = 4;
            this.ctx.beginPath();
            this.ctx.moveTo(this.trajectory.startX, this.trajectory.startY);

            // Draw curve up to current progress
            for (let t = 0; t <= this.trajectory.progress; t += 0.01) {
                const point = this.getPointOnCurve(t);
                this.ctx.lineTo(point.x, point.y);
            }
            this.ctx.stroke();
        }
    }

    drawPlayersOnTrajectory() {
        // Draw cashed out players as circles on the trajectory
        this.cashedOutPlayers.forEach(player => {
            const point = this.getPointOnCurve(player.position);

            // Draw player cash out circle with glow effect
            this.ctx.save();
            this.ctx.shadowColor = '#00c851';
            this.ctx.shadowBlur = 15;

            // Draw outer glow circle
            this.ctx.fillStyle = 'rgba(0, 200, 81, 0.3)';
            this.ctx.beginPath();
            this.ctx.arc(point.x, point.y, 25, 0, Math.PI * 2);
            this.ctx.fill();

            // Draw main circle
            this.ctx.fillStyle = '#00c851';
            this.ctx.beginPath();
            this.ctx.arc(point.x, point.y, 18, 0, Math.PI * 2);
            this.ctx.fill();

            // Draw cash out symbol
            this.ctx.shadowBlur = 0;
            this.ctx.fillStyle = 'white';
            this.ctx.font = 'bold 12px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('$', point.x, point.y + 4);

            // Draw cash out info tooltip
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
            this.ctx.fillRect(point.x - 45, point.y - 55, 90, 30);

            this.ctx.fillStyle = '#00c851';
            this.ctx.font = 'bold 10px Arial';
            this.ctx.fillText(`${player.cashOutMultiplier.toFixed(2)}x`, point.x, point.y - 40);

            this.ctx.fillStyle = 'white';
            this.ctx.font = '9px Arial';
            this.ctx.fillText(`$${(player.bet * player.cashOutMultiplier).toFixed(2)}`, point.x, point.y - 30);

            this.ctx.restore();
        });
    }

    drawModernRocketTrail() {
        if (this.rocket.trail.length < 2) return;

        const currentTime = Date.now();

        // Draw glowing trail
        this.ctx.strokeStyle = 'rgba(66, 133, 244, 0.8)';
        this.ctx.lineWidth = 6;
        this.ctx.lineCap = 'round';
        this.ctx.shadowColor = '#4285f4';
        this.ctx.shadowBlur = 15;

        this.ctx.beginPath();
        this.rocket.trail.forEach((point, index) => {
            if (index === 0) {
                this.ctx.moveTo(point.x, point.y);
            } else {
                this.ctx.lineTo(point.x, point.y);
            }
        });
        this.ctx.stroke();

        // Reset shadow
        this.ctx.shadowBlur = 0;
    }

    drawModernRocket() {
        this.ctx.save();
        this.ctx.translate(this.rocket.x, this.rocket.y);
        this.ctx.rotate(this.rocket.angle);

        // Draw rocket glow
        if (this.gameState === 'flying') {
            this.ctx.shadowColor = '#4285f4';
            this.ctx.shadowBlur = 20;
        }

        // Draw rocket body (modern blue design)
        const bodyGradient = this.ctx.createLinearGradient(-20, -10, -20, 10);
        bodyGradient.addColorStop(0, '#4285f4');
        bodyGradient.addColorStop(0.5, '#1976d2');
        bodyGradient.addColorStop(1, '#4285f4');

        this.ctx.fillStyle = bodyGradient;
        this.ctx.fillRect(-20, -8, 35, 16);

        // Draw rocket nose
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.moveTo(15, 0);
        this.ctx.lineTo(25, -8);
        this.ctx.lineTo(25, 8);
        this.ctx.closePath();
        this.ctx.fill();

        // Draw rocket fins
        this.ctx.fillStyle = '#1976d2';
        this.ctx.fillRect(-20, -12, 10, 8);
        this.ctx.fillRect(-20, 4, 10, 8);

        // Draw flame effect
        if (this.gameState === 'flying') {
            const time = Date.now() * 0.01;
            const flameLength = 15 + Math.sin(time) * 5;

            const flameGradient = this.ctx.createLinearGradient(-20, 0, -20 - flameLength, 0);
            flameGradient.addColorStop(0, 'rgba(255, 193, 7, 0.9)');
            flameGradient.addColorStop(0.5, 'rgba(255, 87, 34, 0.7)');
            flameGradient.addColorStop(1, 'rgba(255, 193, 7, 0)');

            this.ctx.fillStyle = flameGradient;
            this.ctx.fillRect(-20 - flameLength, -6, flameLength, 12);
        }

        this.ctx.shadowBlur = 0;
        this.ctx.restore();
    }

    drawGameUI() {
        // This method can be used for any fixed UI elements
        // Most UI is handled by HTML/CSS now
    }

    drawAnimatedBackground() {
        const time = Date.now() * 0.001;

        // Create extended background for camera movement
        const bgWidth = this.canvas.width + Math.abs(this.camera.x) + 200;
        const bgHeight = this.canvas.height + Math.abs(this.camera.y) + 200;
        const bgX = Math.min(0, this.camera.x - 100);
        const bgY = Math.min(0, this.camera.y - 100);

        const gradient = this.ctx.createLinearGradient(bgX, bgY, bgX, bgY + bgHeight);

        const r1 = Math.sin(time * 0.5) * 20 + 10;
        const g1 = Math.sin(time * 0.3) * 30 + 40;
        const b1 = Math.sin(time * 0.7) * 40 + 80;

        gradient.addColorStop(0, `rgb(${r1}, ${g1}, ${b1})`);
        gradient.addColorStop(0.5, '#001122');
        gradient.addColorStop(1, '#004e92');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
    }
    
    drawTwinklingStars() {
        const time = Date.now() * 0.001;

        // Create extended star field for camera movement
        const starFieldWidth = this.canvas.width + Math.abs(this.camera.x) + 400;
        const starFieldHeight = this.canvas.height + Math.abs(this.camera.y) + 400;
        const offsetX = Math.min(0, this.camera.x - 200);
        const offsetY = Math.min(0, this.camera.y - 200);

        for (let i = 0; i < 200; i++) {
            const x = offsetX + (i * 37) % starFieldWidth;
            const y = offsetY + (i * 23) % (starFieldHeight * 0.7);
            const twinkle = Math.sin(time * 2 + i) * 0.5 + 0.5;
            const size = (Math.sin(i) * 0.5 + 1) * twinkle;

            this.ctx.fillStyle = `rgba(255, 255, 255, ${twinkle})`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }
    
    drawRocketTrail() {
        if (this.rocket.trail.length < 2) return;
        
        const trailLayers = [
            { color: '#ffc107', width: 8, alpha: 0.4 },
            { color: '#ff8f00', width: 5, alpha: 0.6 },
            { color: '#ffeb3b', width: 2, alpha: 0.8 }
        ];
        
        trailLayers.forEach(layer => {
            this.ctx.strokeStyle = layer.color;
            this.ctx.lineWidth = layer.width;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';
            
            this.ctx.beginPath();
            this.ctx.moveTo(this.rocket.trail[0].x, this.rocket.trail[0].y);
            
            for (let i = 1; i < this.rocket.trail.length; i++) {
                const point = this.rocket.trail[i];
                const alpha = (i / this.rocket.trail.length) * layer.alpha;
                this.ctx.globalAlpha = alpha;
                this.ctx.lineTo(point.x, point.y);
            }
            
            this.ctx.stroke();
            this.ctx.globalAlpha = 1;
        });
    }
    
    drawEnhancedRocket() {
        this.ctx.save();
        this.ctx.translate(this.rocket.x, this.rocket.y);
        this.ctx.rotate(this.rocket.angle);

        // Draw rocket glow/aura when flying
        if (this.gameState === 'flying') {
            const glowIntensity = Math.min(this.multiplier / 5, 1);
            this.ctx.shadowColor = '#ffc107';
            this.ctx.shadowBlur = 20 * glowIntensity;

            // Draw outer glow
            this.ctx.fillStyle = `rgba(255, 193, 7, ${0.1 * glowIntensity})`;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, 40 * glowIntensity, 0, Math.PI * 2);
            this.ctx.fill();
        }

        // Draw rocket shadow
        this.ctx.save();
        this.ctx.translate(3, 3);
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(-15, -6, 30, 12);
        this.ctx.restore();

        // Draw rocket body
        const bodyGradient = this.ctx.createLinearGradient(-15, -8, -15, 8);
        bodyGradient.addColorStop(0, '#ffc107');
        bodyGradient.addColorStop(0.5, '#ff8f00');
        bodyGradient.addColorStop(1, '#ffc107');
        this.ctx.fillStyle = bodyGradient;
        this.ctx.fillRect(-15, -6, 30, 12);

        // Draw rocket details
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(-10, -4, 20, 2);
        this.ctx.fillRect(-10, 2, 20, 2);

        // Draw rocket nose
        const noseGradient = this.ctx.createLinearGradient(15, -6, 25, 0);
        noseGradient.addColorStop(0, '#ffeb3b');
        noseGradient.addColorStop(1, '#ffc107');
        this.ctx.fillStyle = noseGradient;
        this.ctx.beginPath();
        this.ctx.moveTo(15, 0);
        this.ctx.lineTo(25, -6);
        this.ctx.lineTo(25, 6);
        this.ctx.closePath();
        this.ctx.fill();

        // Draw rocket fins
        this.ctx.fillStyle = '#ff8f00';
        this.ctx.fillRect(-15, -10, 8, 8);
        this.ctx.fillRect(-15, 2, 8, 8);

        // Draw enhanced flame effect
        if (this.gameState === 'flying') {
            const time = Date.now() * 0.01;
            const flameLength = 20 + Math.sin(time) * 8;

            // Main flame
            const flameGradient = this.ctx.createLinearGradient(-30, 0, -15, 0);
            flameGradient.addColorStop(0, '#ffeb3b');
            flameGradient.addColorStop(0.5, '#ff9800');
            flameGradient.addColorStop(1, '#ff5722');

            this.ctx.fillStyle = flameGradient;
            this.ctx.fillRect(-15 - flameLength, -5, flameLength, 10);

            // Inner flame
            this.ctx.fillStyle = '#ffeb3b';
            const innerFlameLength = flameLength * 0.6;
            this.ctx.fillRect(-15 - innerFlameLength, -3, innerFlameLength, 6);

            // Flame particles
            for (let i = 0; i < 6; i++) {
                const particleX = -25 - Math.random() * 15;
                const particleY = (Math.random() - 0.5) * 8;
                const particleSize = Math.random() * 2 + 1;

                this.ctx.fillStyle = `rgba(255, ${Math.floor(Math.random() * 100 + 155)}, 0, ${Math.random()})`;
                this.ctx.beginPath();
                this.ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
                this.ctx.fill();
            }
        }

        // Reset shadow
        this.ctx.shadowBlur = 0;
        this.ctx.restore();
    }
    
    drawEnhancedGround() {
        const groundHeight = 80;
        const groundY = this.canvas.height - groundHeight;

        // Create extended ground for camera movement
        const groundWidth = this.canvas.width + Math.abs(this.camera.x) + 400;
        const groundX = Math.min(0, this.camera.x - 200);

        // Draw ground
        const groundGradient = this.ctx.createLinearGradient(groundX, groundY, groundX, this.canvas.height);
        groundGradient.addColorStop(0, '#2d3436');
        groundGradient.addColorStop(1, '#636e72');
        this.ctx.fillStyle = groundGradient;
        this.ctx.fillRect(groundX, groundY, groundWidth, groundHeight);

        // Draw launch pad at original position
        this.ctx.fillStyle = '#ffc107';
        this.ctx.fillRect(this.canvas.width * 0.15, groundY - 20, 60, 20);

        // Add some ground details
        this.ctx.fillStyle = '#555';
        for (let i = 0; i < 10; i++) {
            const detailX = groundX + (i * 100);
            this.ctx.fillRect(detailX, groundY + 20, 50, 5);
        }
    }

    drawUI() {
        // Draw any UI elements that should stay fixed on screen
        // (These are not affected by camera movement)

        // Draw altitude and distance indicators
        if (this.gameState === 'flying') {
            const altitude = Math.max(0, this.rocket.baseY - this.rocket.y);
            const distance = Math.max(0, this.rocket.x - this.canvas.width * 0.2);

            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(10, 10, 200, 60);

            this.ctx.fillStyle = 'rgba(255, 193, 7, 0.9)';
            this.ctx.font = 'bold 14px Orbitron';
            this.ctx.fillText(`Altitude: ${Math.floor(altitude)}m`, 20, 30);
            this.ctx.fillText(`Distance: ${Math.floor(distance)}m`, 20, 50);
        }

        // Draw rocket position indicator (mini-map style)
        if (this.gameState === 'flying') {
            const miniMapX = this.canvas.width - 120;
            const miniMapY = 10;
            const miniMapWidth = 100;
            const miniMapHeight = 60;

            // Mini-map background
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(miniMapX, miniMapY, miniMapWidth, miniMapHeight);
            this.ctx.strokeStyle = 'rgba(255, 193, 7, 0.5)';
            this.ctx.strokeRect(miniMapX, miniMapY, miniMapWidth, miniMapHeight);

            // Rocket position on mini-map
            const rocketMapX = miniMapX + (this.rocket.x / (this.canvas.width * 2)) * miniMapWidth;
            const rocketMapY = miniMapY + miniMapHeight - ((this.rocket.baseY - this.rocket.y) / 400) * miniMapHeight;

            this.ctx.fillStyle = '#ffc107';
            this.ctx.beginPath();
            this.ctx.arc(Math.max(miniMapX, Math.min(miniMapX + miniMapWidth, rocketMapX)),
                        Math.max(miniMapY, Math.min(miniMapY + miniMapHeight, rocketMapY)), 3, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }
    
    renderCrash() {
        // Render the normal scene first
        this.render();

        // Add crash effect overlay
        this.ctx.save();

        // Apply camera transform for crash effect
        this.ctx.translate(-this.camera.x, -this.camera.y);
        this.ctx.translate(this.rocket.x, this.rocket.y);

        // Draw explosion
        const time = Date.now() * 0.01;
        for (let i = 0; i < 8; i++) {
            this.ctx.beginPath();
            const radius = 15 + i * 12 + Math.sin(time + i) * 5;
            this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
            this.ctx.fillStyle = `rgba(255, ${107 - i * 15}, ${107 - i * 15}, ${0.9 - i * 0.1})`;
            this.ctx.fill();
        }

        // Add explosion particles
        for (let i = 0; i < 20; i++) {
            const angle = (i / 20) * Math.PI * 2;
            const distance = 30 + Math.random() * 40;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;

            this.ctx.fillStyle = `rgba(255, ${Math.random() * 100 + 155}, 0, ${Math.random()})`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, Math.random() * 4 + 2, 0, Math.PI * 2);
            this.ctx.fill();
        }

        this.ctx.restore();
    }
}

// Scroll to rocket game function
function scrollToRocketGame() {
    const rocketSection = document.getElementById('rocket-game');
    if (rocketSection) {
        rocketSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.rocketGame = null;

    // Function to initialize the game
    window.initializeRocketGame = function() {
        if (!window.rocketGame) {
            window.rocketGame = new RocketGameEngine();
            if (window.rocketGame.canvas) {
                window.rocketGame.startNewRound();
            }
        }
    };

    // Check if user is logged in and initialize game
    const checkUserAndStartGame = () => {
        const userSection = document.getElementById('userSection');
        if (userSection && !userSection.classList.contains('d-none')) {
            document.getElementById('gameLoginPrompt').classList.add('d-none');
            document.getElementById('gameInterface').classList.remove('d-none');
            initializeRocketGame();
        }
    };

    // Initial check
    setTimeout(checkUserAndStartGame, 500);

    // Make functions globally available
    window.checkUserAndStartGame = checkUserAndStartGame;
    window.scrollToRocketGame = scrollToRocketGame;
});
