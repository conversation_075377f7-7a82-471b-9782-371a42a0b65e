#!/usr/bin/env python3
"""
Network connectivity test and firewall helper for Casino Template
"""

import socket
import subprocess
import sys
import platform

def test_port_binding(host, port):
    """Test if we can bind to the specified host and port"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((host, port))
            print(f"✅ Can bind to {host}:{port}")
            return True
    except Exception as e:
        print(f"❌ Cannot bind to {host}:{port} - {e}")
        return False

def get_network_interfaces():
    """Get all network interfaces and their IP addresses"""
    import socket
    hostname = socket.gethostname()
    
    print(f"🖥️  Computer name: {hostname}")
    print("🌐 Network interfaces:")
    
    try:
        # Get all IP addresses for this host
        addresses = socket.getaddrinfo(hostname, None)
        ips = set()
        for addr in addresses:
            ip = addr[4][0]
            if not ip.startswith('::') and ip != '127.0.0.1':  # Skip IPv6 and localhost
                ips.add(ip)
        
        for ip in sorted(ips):
            print(f"   📡 {ip}")
            
        return list(ips)
    except Exception as e:
        print(f"   ❌ Error getting interfaces: {e}")
        return []

def check_firewall_windows():
    """Check Windows Firewall status"""
    if platform.system() != "Windows":
        return
    
    print("\n🔥 Windows Firewall Check:")
    try:
        # Check if firewall is enabled
        result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            if "ON" in result.stdout:
                print("   🔒 Windows Firewall is ENABLED")
                print("   ⚠️  This might be blocking network access")
            else:
                print("   🔓 Windows Firewall is disabled")
        else:
            print("   ❓ Could not check firewall status")
    except Exception as e:
        print(f"   ❌ Error checking firewall: {e}")

def create_firewall_rule():
    """Create Windows Firewall rule for Python"""
    if platform.system() != "Windows":
        print("🔥 Firewall rules are only needed on Windows")
        return
    
    print("\n🔧 Creating Windows Firewall Rule...")
    print("⚠️  This requires Administrator privileges!")
    
    # Get Python executable path
    python_exe = sys.executable
    
    commands = [
        # Allow Python through firewall
        ['netsh', 'advfirewall', 'firewall', 'add', 'rule', 
         'name=Casino Template Python Server', 'dir=in', 'action=allow', 
         f'program={python_exe}', 'enable=yes'],
        
        # Allow port 8000
        ['netsh', 'advfirewall', 'firewall', 'add', 'rule', 
         'name=Casino Template Port 8000', 'dir=in', 'action=allow', 
         'protocol=TCP', 'localport=8000']
    ]
    
    for cmd in commands:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ Rule created: {' '.join(cmd[5:8])}")
            else:
                print(f"   ❌ Failed to create rule: {result.stderr}")
        except Exception as e:
            print(f"   ❌ Error creating rule: {e}")

def test_network_connectivity(target_ip, port=8000):
    """Test if we can connect to the target IP and port"""
    print(f"\n🔍 Testing connectivity to {target_ip}:{port}")
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            result = s.connect_ex((target_ip, port))
            if result == 0:
                print(f"   ✅ Can connect to {target_ip}:{port}")
                return True
            else:
                print(f"   ❌ Cannot connect to {target_ip}:{port}")
                return False
    except Exception as e:
        print(f"   ❌ Connection test failed: {e}")
        return False

def main():
    print("🎰 Casino Template Network Diagnostics")
    print("=" * 50)
    
    target_ip = "*************"
    port = 8000
    
    # Test 1: Get network interfaces
    interfaces = get_network_interfaces()
    
    # Test 2: Check if target IP is available
    if target_ip not in interfaces:
        print(f"\n⚠️  Warning: {target_ip} not found in network interfaces!")
        print("Available IPs:")
        for ip in interfaces:
            print(f"   📡 {ip}")
        
        if interfaces:
            suggested_ip = interfaces[0]
            print(f"\n💡 Suggestion: Try using {suggested_ip} instead")
    
    # Test 3: Test port binding
    print(f"\n🔌 Testing port binding:")
    can_bind = test_port_binding(target_ip, port)
    
    # Test 4: Check firewall
    check_firewall_windows()
    
    # Test 5: Provide solutions
    print("\n🔧 Troubleshooting Steps:")
    print("1. Make sure you're using the correct IP address")
    print("2. Check Windows Firewall settings")
    print("3. Try running as Administrator")
    print("4. Test with a different port")
    
    # Offer to create firewall rule
    if platform.system() == "Windows":
        print("\n❓ Would you like to create Windows Firewall rules?")
        print("   This requires running as Administrator")
        response = input("   Create firewall rules? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            create_firewall_rule()
    
    print("\n🚀 Recommended next steps:")
    print("1. Run this script as Administrator (Windows)")
    print("2. Start the server with: python server.py")
    print("3. Test from another device")
    
    # Show test URLs
    print(f"\n🌐 Test URLs:")
    print(f"   Local: http://localhost:{port}")
    print(f"   Network: http://{target_ip}:{port}")
    for ip in interfaces:
        if ip != target_ip:
            print(f"   Alternative: http://{ip}:{port}")

if __name__ == "__main__":
    main()
