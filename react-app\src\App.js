import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from './components/Header/Header';
import Hero from './components/Hero/Hero';
import RocketGame from './components/RocketGame/RocketGame';
import ResponsibleGaming from './components/ResponsibleGaming/ResponsibleGaming';
import Footer from './components/Footer/Footer';
import AuthModal from './components/Auth/AuthModal';
import AgeVerificationModal from './components/Modals/AgeVerificationModal';
import { UserProvider } from './contexts/UserContext';
import { GameProvider } from './contexts/GameContext';
import './styles/App.css';

const App = () => {
  const [currentSection, setCurrentSection] = useState('home');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState('login'); // 'login' or 'register'
  const [showAgeVerification, setShowAgeVerification] = useState(false);

  useEffect(() => {
    // Check age verification on app load
    const ageVerified = localStorage.getItem('ageVerified');
    if (!ageVerified) {
      setShowAgeVerification(true);
    }
  }, []);

  const handleNavigation = (section) => {
    setCurrentSection(section);
    
    // Smooth scroll to section
    const element = document.getElementById(section);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleAuthModal = (mode) => {
    setAuthMode(mode);
    setShowAuthModal(true);
  };

  const handleAgeVerification = (confirmed) => {
    if (confirmed) {
      localStorage.setItem('ageVerified', 'true');
      setShowAgeVerification(false);
    } else {
      // Redirect to external site
      window.location.href = 'https://www.google.com';
    }
  };

  return (
    <UserProvider>
      <GameProvider>
        <div className="app">
          <Header 
            currentSection={currentSection}
            onNavigate={handleNavigation}
            onAuth={handleAuthModal}
          />
          
          <main className="main-content">
            <motion.section 
              id="home"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <Hero onNavigate={handleNavigation} onAuth={handleAuthModal} />
            </motion.section>

            <motion.section 
              id="game"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <RocketGame />
            </motion.section>

            <motion.section 
              id="responsible"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <ResponsibleGaming />
            </motion.section>
          </main>

          <Footer />

          <AnimatePresence>
            {showAuthModal && (
              <AuthModal
                mode={authMode}
                onClose={() => setShowAuthModal(false)}
                onSwitchMode={(mode) => setAuthMode(mode)}
              />
            )}
          </AnimatePresence>

          <AnimatePresence>
            {showAgeVerification && (
              <AgeVerificationModal
                onConfirm={() => handleAgeVerification(true)}
                onDeny={() => handleAgeVerification(false)}
              />
            )}
          </AnimatePresence>
        </div>
      </GameProvider>
    </UserProvider>
  );
};

export default App;
