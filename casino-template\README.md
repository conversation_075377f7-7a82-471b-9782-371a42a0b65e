# 🎰 Casino Template - Rocket Game

A modern, responsive casino template featuring a rocket crash game with curved trajectory, real-time multiplayer, and professional UI design.

## 🚀 Quick Start

### Option 1: Easy Start (Recommended)

**Windows:**
```bash
# Double-click the file or run in command prompt
start_server.bat
```

**Linux/Mac:**
```bash
# Make executable and run
chmod +x start_server.sh
./start_server.sh
```

### Option 2: Manual Start

```bash
# Make sure you're in the casino-template directory
cd casino-template

# Start the Python server
python server.py
# or on some systems:
python3 server.py
```

## 📋 Requirements

- **Python 3.6+** (Download from [python.org](https://python.org))
- **Modern web browser** (Chrome, Firefox, Safari, Edge)

## 🎮 Features

### Rocket Game
- **Curved trajectory** with smooth rocket movement
- **Dynamic camera** that follows the rocket
- **Real-time multiplier** display
- **Cash out system** with live win calculations
- **Auto-bet functionality**
- **Professional UI** with modern design

### User System
- **Real user authentication** (no fake players)
- **Balance management**
- **Game statistics tracking**
- **Responsive design** for all devices

### Technical Features
- **Pure HTML/CSS/JavaScript** - no frameworks required
- **Local storage** for user data persistence
- **Responsive design** for mobile and desktop
- **Modern ES6+ JavaScript**
- **Professional animations** and effects

## 🌐 Server Features

- **Auto port detection** - finds available port automatically
- **CORS enabled** for development
- **Proper MIME types** for all file types
- **Auto browser opening** - opens your default browser automatically
- **Development optimized** with no caching
- **Error handling** with helpful messages

## 📁 Project Structure

```
casino-template/
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── style.css       # Main stylesheet
│   ├── js/
│   │   ├── rocket-game.js  # Rocket game engine
│   │   ├── user-manager.js # User management
│   │   └── main.js         # Main application logic
│   └── images/             # Image assets
├── server.py               # Python web server
├── start_server.bat        # Windows start script
├── start_server.sh         # Linux/Mac start script
└── README.md              # This file
```

## 🎯 How to Play

1. **Start the server** using one of the methods above
2. **Open your browser** to the displayed URL (usually http://localhost:8000)
3. **Create an account** or log in
4. **Navigate to the Rocket Game** section
5. **Place your bet** and watch the rocket fly!
6. **Cash out** before the rocket crashes to win

## 🔧 Development

### Customization
- Edit `assets/css/style.css` for styling changes
- Modify `assets/js/rocket-game.js` for game mechanics
- Update `assets/js/user-manager.js` for user system changes

### Adding Features
- The codebase is modular and easy to extend
- Add new games by creating additional JavaScript modules
- Integrate with real backend APIs for production use

## 🌟 Production Deployment

For production deployment, consider:
- Using a proper web server (nginx, Apache)
- Implementing real user authentication
- Adding database integration
- Setting up HTTPS
- Implementing real multiplayer with WebSockets

## 🐛 Troubleshooting

### Server won't start
- Make sure Python is installed: `python --version`
- Check if port is available (server will auto-find free port)
- Ensure you're in the correct directory

### Game not loading
- Check browser console for JavaScript errors
- Ensure all files are present in the assets folder
- Try refreshing the page (Ctrl+F5)

### Styling issues
- Clear browser cache
- Check if CSS files are loading properly
- Verify file paths in HTML

## 📞 Support

If you encounter any issues:
1. Check the browser console for errors
2. Verify all files are present
3. Ensure Python is properly installed
4. Try a different browser

## 🎉 Enjoy!

Your casino template is now ready to use! The server will automatically open your browser to the game.

**Happy Gaming! 🎰**
