@echo off
echo 🎰 Casino Template Firewall Fix
echo ================================
echo.
echo This will create Windows Firewall rules to allow network access.
echo You will be prompted for Administrator privileges.
echo.
pause

REM Run PowerShell script as Administrator
powershell -Command "Start-Process PowerShell -ArgumentList '-ExecutionPolicy Bypass -File \"%~dp0fix_firewall.ps1\"' -Verb RunAs"

echo.
echo ✅ Firewall fix completed!
echo You can now start the network server.
pause
