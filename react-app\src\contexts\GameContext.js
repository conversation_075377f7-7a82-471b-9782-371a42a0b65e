import React, { createContext, useContext, useReducer, useCallback } from 'react';

const GameContext = createContext();

const initialState = {
  gameState: 'waiting', // waiting, countdown, flying, crashed
  multiplier: 1.00,
  crashPoint: 0,
  currentBet: 0,
  hasActiveBet: false,
  hasCashedOut: false,
  gameHistory: [],
  isGameRunning: false,
  countdown: 0,
  elapsedTime: 0,
  rocket: {
    x: 50,
    y: 350,
    angle: 0,
    trail: [],
  },
};

const gameReducer = (state, action) => {
  switch (action.type) {
    case 'SET_GAME_STATE':
      return { ...state, gameState: action.payload };
    
    case 'SET_MULTIPLIER':
      return { ...state, multiplier: action.payload };
    
    case 'SET_CRASH_POINT':
      return { ...state, crashPoint: action.payload };
    
    case 'PLACE_BET':
      return {
        ...state,
        currentBet: action.payload,
        hasActiveBet: true,
        hasCashedOut: false,
      };
    
    case 'CASH_OUT':
      return {
        ...state,
        hasCashedOut: true,
      };
    
    case 'RESET_GAME':
      return {
        ...state,
        gameState: 'waiting',
        multiplier: 1.00,
        currentBet: 0,
        hasActiveBet: false,
        hasCashedOut: false,
        elapsedTime: 0,
        rocket: {
          ...initialState.rocket,
        },
      };
    
    case 'UPDATE_ROCKET':
      return {
        ...state,
        rocket: { ...state.rocket, ...action.payload },
      };
    
    case 'ADD_TO_HISTORY':
      return {
        ...state,
        gameHistory: [action.payload, ...state.gameHistory.slice(0, 9)],
      };
    
    case 'SET_COUNTDOWN':
      return { ...state, countdown: action.payload };
    
    case 'SET_ELAPSED_TIME':
      return { ...state, elapsedTime: action.payload };
    
    case 'UPDATE_TRAIL':
      return {
        ...state,
        rocket: {
          ...state.rocket,
          trail: action.payload,
        },
      };
    
    default:
      return state;
  }
};

export const GameProvider = ({ children }) => {
  const [state, dispatch] = useReducer(gameReducer, initialState);

  const generateCrashPoint = useCallback(() => {
    // Generate crash point using exponential distribution
    const random = Math.random();
    const crashPoint = Math.max(
      1.01,
      Math.min(50.0, 1 / (1 - random * 0.99))
    );
    return Math.round(crashPoint * 100) / 100;
  }, []);

  const placeBet = useCallback((amount) => {
    if (state.gameState !== 'waiting') return false;
    
    dispatch({ type: 'PLACE_BET', payload: amount });
    return true;
  }, [state.gameState]);

  const cashOut = useCallback(() => {
    if (!state.hasActiveBet || state.hasCashedOut || state.gameState !== 'flying') {
      return false;
    }
    
    dispatch({ type: 'CASH_OUT' });
    return true;
  }, [state.hasActiveBet, state.hasCashedOut, state.gameState]);

  const startNewRound = useCallback(() => {
    const crashPoint = generateCrashPoint();
    
    dispatch({ type: 'RESET_GAME' });
    dispatch({ type: 'SET_CRASH_POINT', payload: crashPoint });
    
    // Start countdown
    let countdown = 5;
    dispatch({ type: 'SET_COUNTDOWN', payload: countdown });
    dispatch({ type: 'SET_GAME_STATE', payload: 'countdown' });
    
    const countdownInterval = setInterval(() => {
      countdown--;
      dispatch({ type: 'SET_COUNTDOWN', payload: countdown });
      
      if (countdown <= 0) {
        clearInterval(countdownInterval);
        startFlight();
      }
    }, 1000);
  }, [generateCrashPoint]);

  const startFlight = useCallback(() => {
    dispatch({ type: 'SET_GAME_STATE', payload: 'flying' });
    
    const startTime = Date.now();
    
    const gameLoop = () => {
      const currentTime = Date.now();
      const elapsedTime = (currentTime - startTime) / 1000;
      
      // Calculate multiplier
      const multiplier = Math.pow(1.1, elapsedTime * 2);
      
      dispatch({ type: 'SET_MULTIPLIER', payload: multiplier });
      dispatch({ type: 'SET_ELAPSED_TIME', payload: elapsedTime });
      
      // Update rocket position
      const newRocket = {
        x: 50 + (elapsedTime * 60),
        y: 350 - (elapsedTime * 40),
        angle: Math.sin(elapsedTime * 2) * 0.2,
      };
      
      dispatch({ type: 'UPDATE_ROCKET', payload: newRocket });
      
      // Check if crashed
      if (multiplier >= state.crashPoint) {
        dispatch({ type: 'SET_GAME_STATE', payload: 'crashed' });
        dispatch({ type: 'ADD_TO_HISTORY', payload: state.crashPoint });
        
        // Start new round after delay
        setTimeout(() => {
          startNewRound();
        }, 3000);
        
        return;
      }
      
      // Continue game loop
      requestAnimationFrame(gameLoop);
    };
    
    gameLoop();
  }, [state.crashPoint, startNewRound]);

  const updateRocketTrail = useCallback((newTrailPoint) => {
    const newTrail = [...state.rocket.trail, newTrailPoint];
    if (newTrail.length > 50) {
      newTrail.shift();
    }
    dispatch({ type: 'UPDATE_TRAIL', payload: newTrail });
  }, [state.rocket.trail]);

  const value = {
    ...state,
    placeBet,
    cashOut,
    startNewRound,
    updateRocketTrail,
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
};

export const useGame = () => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
};
