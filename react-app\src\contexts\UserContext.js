import React, { createContext, useContext, useReducer, useEffect } from 'react';

const UserContext = createContext();

const initialState = {
  currentUser: null,
  users: {},
  isLoading: false,
  error: null,
};

const userReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        currentUser: action.payload,
        isLoading: false,
        error: null,
      };
    
    case 'LOGOUT':
      return {
        ...state,
        currentUser: null,
        error: null,
      };
    
    case 'REGISTER_SUCCESS':
      return {
        ...state,
        currentUser: action.payload.user,
        users: { ...state.users, [action.payload.user.username]: action.payload.user },
        isLoading: false,
        error: null,
      };
    
    case 'UPDATE_BALANCE':
      const updatedUser = { ...state.currentUser, balance: action.payload };
      return {
        ...state,
        currentUser: updatedUser,
        users: { ...state.users, [updatedUser.username]: updatedUser },
      };
    
    case 'LOAD_USERS':
      return {
        ...state,
        users: action.payload,
      };
    
    default:
      return state;
  }
};

export const UserProvider = ({ children }) => {
  const [state, dispatch] = useReducer(userReducer, initialState);

  useEffect(() => {
    // Load users from localStorage on app start
    const savedUsers = localStorage.getItem('gamblingUsers');
    if (savedUsers) {
      dispatch({ type: 'LOAD_USERS', payload: JSON.parse(savedUsers) });
    }
  }, []);

  const saveUsers = (users) => {
    localStorage.setItem('gamblingUsers', JSON.stringify(users));
  };

  const login = async (username, password) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const user = state.users[username];
      if (!user || user.password !== password) {
        throw new Error('Invalid username or password');
      }
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: user });
      return { success: true };
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      return { success: false, error: error.message };
    }
  };

  const register = async (userData) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (state.users[userData.username]) {
        throw new Error('Username already exists');
      }
      
      const newUser = {
        ...userData,
        balance: 1000.00,
        registeredAt: new Date().toISOString(),
        stats: {
          gamesPlayed: 0,
          totalWagered: 0,
          totalWon: 0,
        },
      };
      
      const updatedUsers = { ...state.users, [newUser.username]: newUser };
      saveUsers(updatedUsers);
      
      dispatch({ 
        type: 'REGISTER_SUCCESS', 
        payload: { user: newUser, users: updatedUsers } 
      });
      
      return { success: true };
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      return { success: false, error: error.message };
    }
  };

  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  const updateBalance = (newBalance) => {
    if (state.currentUser) {
      const updatedUsers = {
        ...state.users,
        [state.currentUser.username]: {
          ...state.currentUser,
          balance: newBalance,
        },
      };
      saveUsers(updatedUsers);
      dispatch({ type: 'UPDATE_BALANCE', payload: newBalance });
    }
  };

  const updateStats = (wagered, won) => {
    if (state.currentUser) {
      const updatedUser = {
        ...state.currentUser,
        stats: {
          gamesPlayed: state.currentUser.stats.gamesPlayed + 1,
          totalWagered: state.currentUser.stats.totalWagered + wagered,
          totalWon: state.currentUser.stats.totalWon + won,
        },
      };
      
      const updatedUsers = { ...state.users, [updatedUser.username]: updatedUser };
      saveUsers(updatedUsers);
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: updatedUser });
    }
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    updateBalance,
    updateStats,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
