# PowerShell script to fix Windows Firewall for Casino Template
# Run as Administrator

Write-Host "🎰 Casino Template Firewall Fix" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Running as Administrator" -ForegroundColor Green

# Get Python executable path
$pythonPath = (Get-Command python -ErrorAction SilentlyContinue).Source
if (-not $pythonPath) {
    Write-Host "❌ Python not found in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🐍 Python found at: $pythonPath" -ForegroundColor Green

# Create firewall rules
Write-Host "🔧 Creating Windows Firewall rules..." -ForegroundColor Yellow

try {
    # Rule 1: Allow Python executable
    New-NetFirewallRule -DisplayName "Casino Template Python Server" -Direction Inbound -Action Allow -Program $pythonPath -Enabled True
    Write-Host "✅ Created rule for Python executable" -ForegroundColor Green
    
    # Rule 2: Allow port 8000
    New-NetFirewallRule -DisplayName "Casino Template Port 8000" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8000 -Enabled True
    Write-Host "✅ Created rule for port 8000" -ForegroundColor Green
    
    # Rule 3: Allow port range 8000-8010 (in case port 8000 is busy)
    New-NetFirewallRule -DisplayName "Casino Template Ports 8000-8010" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8000-8010 -Enabled True
    Write-Host "✅ Created rule for ports 8000-8010" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🎉 Firewall rules created successfully!" -ForegroundColor Green
    Write-Host "Your casino template should now be accessible from other devices." -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error creating firewall rules: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🌐 Test URLs:" -ForegroundColor Yellow
Write-Host "   Local: http://localhost:8000" -ForegroundColor Cyan
Write-Host "   Network: http://*************:8000" -ForegroundColor Cyan

Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Yellow
Write-Host "1. Start the server: python server_network.py" -ForegroundColor White
Write-Host "2. Test from another device on your network" -ForegroundColor White
Write-Host "3. If still not working, check your router settings" -ForegroundColor White

Read-Host "Press Enter to exit"
