<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RocketBet React - Next-Gen Gambling Experience</title>
    
    <!-- React CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Framer Motion CDN -->
    <script src="https://unpkg.com/framer-motion@10/dist/framer-motion.js"></script>
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #74b9ff;
            --success-color: #00b894;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-bg: #0a0a0a;
            --dark-secondary: #1a1a2e;
            --dark-tertiary: #16213e;
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --border-color: rgba(255, 107, 107, 0.3);
            --glow-color: rgba(255, 107, 107, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 50%, var(--dark-tertiary) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app {
            min-height: 100vh;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            font-weight: 900;
            color: var(--primary-color);
            cursor: pointer;
        }

        .logo i {
            font-size: 2rem;
            filter: drop-shadow(0 0 10px var(--glow-color));
        }

        .main-content {
            padding-top: 100px;
            min-height: 100vh;
        }

        .hero {
            text-align: center;
            padding: 4rem 0;
            background: radial-gradient(circle at center, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
        }

        .hero h1 {
            font-family: 'Orbitron', monospace;
            font-size: clamp(3rem, 8vw, 6rem);
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--primary-color), #ff8e53);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .highlight {
            color: var(--primary-color);
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            background: linear-gradient(45deg, var(--primary-color), #ee5a24);
            color: white;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }

        .rocket-game {
            padding: 4rem 0;
            background: rgba(0, 0, 0, 0.3);
        }

        .game-title {
            text-align: center;
            margin-bottom: 3rem;
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-family: 'Orbitron', monospace;
            background: linear-gradient(45deg, var(--primary-color), #ff8e53);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .game-canvas-container {
            position: relative;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 20px;
            border: 3px solid var(--border-color);
            overflow: hidden;
            box-shadow: 
                0 15px 40px rgba(0, 0, 0, 0.5),
                inset 0 0 30px rgba(255, 107, 107, 0.1);
            margin-bottom: 2rem;
        }

        .game-canvas {
            width: 100%;
            height: 500px;
            display: block;
        }

        .multiplier-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: 'Orbitron', monospace;
            font-size: 4rem;
            font-weight: 900;
            color: var(--primary-color);
            text-shadow: 
                0 0 20px rgba(255, 107, 107, 0.8),
                0 0 40px rgba(255, 107, 107, 0.6);
            animation: pulse 2s ease-in-out infinite;
            pointer-events: none;
        }

        .game-status {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            border: 2px solid var(--border-color);
        }

        .game-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .control-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .control-section h3 {
            margin-bottom: 1rem;
            color: var(--primary-color);
            font-family: 'Orbitron', monospace;
        }

        .bet-input {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            font-family: 'Orbitron', monospace;
        }

        .bet-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
        }

        .control-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .btn-place-bet {
            background: linear-gradient(45deg, var(--primary-color), #ee5a24);
            color: white;
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
        }

        .btn-cash-out {
            background: linear-gradient(45deg, var(--success-color), #00a085);
            color: white;
            box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);
            animation: cashOutPulse 1.5s ease-in-out infinite;
        }

        .btn-cash-out:disabled {
            background: #666;
            animation: none;
            cursor: not-allowed;
        }

        .control-btn:hover:not(:disabled) {
            transform: translateY(-2px);
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }

        @keyframes cashOutPulse {
            0%, 100% { box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3); }
            50% { box-shadow: 0 6px 30px rgba(0, 184, 148, 0.6); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .rocket-preview {
            font-size: 4rem;
            color: var(--primary-color);
            animation: float 3s ease-in-out infinite;
            margin-top: 2rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .game-controls {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .multiplier-overlay {
                font-size: 2.5rem;
            }
            
            .hero h1 {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useContext, createContext } = React;
        const { motion, AnimatePresence } = Motion;

        // User Context
        const UserContext = createContext();
        
        const UserProvider = ({ children }) => {
            const [currentUser, setCurrentUser] = useState(null);
            const [users, setUsers] = useState({});

            useEffect(() => {
                const savedUsers = localStorage.getItem('gamblingUsers');
                if (savedUsers) {
                    setUsers(JSON.parse(savedUsers));
                }
            }, []);

            const login = (username, password) => {
                const user = users[username];
                if (user && user.password === password) {
                    setCurrentUser(user);
                    return true;
                }
                return false;
            };

            const register = (userData) => {
                if (users[userData.username]) {
                    return false;
                }
                
                const newUser = {
                    ...userData,
                    balance: 1000.00,
                    registeredAt: new Date().toISOString()
                };
                
                const updatedUsers = { ...users, [newUser.username]: newUser };
                setUsers(updatedUsers);
                localStorage.setItem('gamblingUsers', JSON.stringify(updatedUsers));
                setCurrentUser(newUser);
                return true;
            };

            const logout = () => {
                setCurrentUser(null);
            };

            const updateBalance = (newBalance) => {
                if (currentUser) {
                    const updatedUser = { ...currentUser, balance: newBalance };
                    const updatedUsers = { ...users, [currentUser.username]: updatedUser };
                    setUsers(updatedUsers);
                    setCurrentUser(updatedUser);
                    localStorage.setItem('gamblingUsers', JSON.stringify(updatedUsers));
                }
            };

            return (
                <UserContext.Provider value={{
                    currentUser,
                    login,
                    register,
                    logout,
                    updateBalance
                }}>
                    {children}
                </UserContext.Provider>
            );
        };

        const useUser = () => useContext(UserContext);

        // Game Context
        const GameContext = createContext();
        
        const GameProvider = ({ children }) => {
            const [gameState, setGameState] = useState('waiting');
            const [multiplier, setMultiplier] = useState(1.00);
            const [crashPoint, setCrashPoint] = useState(0);
            const [currentBet, setCurrentBet] = useState(0);
            const [hasActiveBet, setHasActiveBet] = useState(false);
            const [hasCashedOut, setHasCashedOut] = useState(false);

            const generateCrashPoint = () => {
                const random = Math.random();
                const crashPoint = Math.max(1.01, Math.min(50.0, 1 / (1 - random * 0.99)));
                return Math.round(crashPoint * 100) / 100;
            };

            const placeBet = (amount) => {
                if (gameState !== 'waiting') return false;
                setCurrentBet(amount);
                setHasActiveBet(true);
                setHasCashedOut(false);
                return true;
            };

            const cashOut = () => {
                if (!hasActiveBet || hasCashedOut || gameState !== 'flying') return false;
                setHasCashedOut(true);
                return true;
            };

            const startNewRound = () => {
                const newCrashPoint = generateCrashPoint();
                setCrashPoint(newCrashPoint);
                setGameState('waiting');
                setMultiplier(1.00);
                setHasActiveBet(false);
                setHasCashedOut(false);
                setCurrentBet(0);

                setTimeout(() => {
                    setGameState('countdown');
                    let countdown = 5;
                    
                    const countdownInterval = setInterval(() => {
                        countdown--;
                        if (countdown <= 0) {
                            clearInterval(countdownInterval);
                            startFlight(newCrashPoint);
                        }
                    }, 1000);
                }, 2000);
            };

            const startFlight = (crashPoint) => {
                setGameState('flying');
                const startTime = Date.now();
                
                const gameLoop = () => {
                    const currentTime = Date.now();
                    const elapsedTime = (currentTime - startTime) / 1000;
                    const newMultiplier = Math.pow(1.1, elapsedTime * 2);
                    
                    setMultiplier(newMultiplier);
                    
                    if (newMultiplier >= crashPoint) {
                        setGameState('crashed');
                        setTimeout(() => startNewRound(), 3000);
                        return;
                    }
                    
                    requestAnimationFrame(gameLoop);
                };
                
                gameLoop();
            };

            return (
                <GameContext.Provider value={{
                    gameState,
                    multiplier,
                    crashPoint,
                    currentBet,
                    hasActiveBet,
                    hasCashedOut,
                    placeBet,
                    cashOut,
                    startNewRound
                }}>
                    {children}
                </GameContext.Provider>
            );
        };

        const useGame = () => useContext(GameContext);

        // Enhanced Game Canvas Component
        const GameCanvas = () => {
            const canvasRef = useRef(null);
            const { gameState, multiplier } = useGame();

            useEffect(() => {
                const canvas = canvasRef.current;
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                canvas.width = 900;
                canvas.height = 500;

                const animate = () => {
                    const time = Date.now() * 0.001;
                    
                    // Clear canvas
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    
                    // Animated background
                    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                    const r1 = Math.sin(time * 0.5) * 20 + 10;
                    const g1 = Math.sin(time * 0.3) * 30 + 40;
                    const b1 = Math.sin(time * 0.7) * 40 + 80;
                    
                    gradient.addColorStop(0, `rgb(${r1}, ${g1}, ${b1})`);
                    gradient.addColorStop(0.5, '#001122');
                    gradient.addColorStop(1, '#004e92');
                    
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // Twinkling stars
                    for (let i = 0; i < 100; i++) {
                        const x = (i * 37) % canvas.width;
                        const y = (i * 23) % (canvas.height * 0.7);
                        const twinkle = Math.sin(time * 2 + i) * 0.5 + 0.5;
                        const size = (Math.sin(i) * 0.5 + 1) * twinkle;
                        
                        ctx.fillStyle = `rgba(255, 255, 255, ${twinkle})`;
                        ctx.beginPath();
                        ctx.arc(x, y, size, 0, Math.PI * 2);
                        ctx.fill();
                    }
                    
                    // Enhanced rocket
                    const rocketX = 100 + (gameState === 'flying' ? (time * 60) % (canvas.width - 200) : 0);
                    const rocketY = canvas.height - 150 - (gameState === 'flying' ? Math.sin(time * 2) * 20 : 0);
                    
                    ctx.save();
                    ctx.translate(rocketX, rocketY);
                    
                    // Rocket body
                    const bodyGradient = ctx.createLinearGradient(-15, -8, -15, 8);
                    bodyGradient.addColorStop(0, '#ff6b6b');
                    bodyGradient.addColorStop(0.5, '#ff8e53');
                    bodyGradient.addColorStop(1, '#ff6b6b');
                    ctx.fillStyle = bodyGradient;
                    ctx.fillRect(-15, -6, 30, 12);
                    
                    // Rocket nose
                    const noseGradient = ctx.createLinearGradient(15, -6, 25, 0);
                    noseGradient.addColorStop(0, '#ffeb3b');
                    noseGradient.addColorStop(1, '#ffc107');
                    ctx.fillStyle = noseGradient;
                    ctx.beginPath();
                    ctx.moveTo(15, 0);
                    ctx.lineTo(25, -6);
                    ctx.lineTo(25, 6);
                    ctx.closePath();
                    ctx.fill();
                    
                    // Flame effect
                    if (gameState === 'flying') {
                        const flameGradient = ctx.createLinearGradient(-30, 0, -15, 0);
                        flameGradient.addColorStop(0, '#ffeb3b');
                        flameGradient.addColorStop(0.5, '#ff9800');
                        flameGradient.addColorStop(1, '#ff5722');
                        
                        ctx.fillStyle = flameGradient;
                        const flameLength = 15 + Math.sin(time * 5) * 5;
                        ctx.fillRect(-15 - flameLength, -4, flameLength, 8);
                    }
                    
                    ctx.restore();
                    
                    // Ground
                    const groundGradient = ctx.createLinearGradient(0, canvas.height - 80, 0, canvas.height);
                    groundGradient.addColorStop(0, '#2d3436');
                    groundGradient.addColorStop(1, '#636e72');
                    ctx.fillStyle = groundGradient;
                    ctx.fillRect(0, canvas.height - 80, canvas.width, 80);
                    
                    requestAnimationFrame(animate);
                };
                
                animate();
            }, [gameState, multiplier]);

            return <canvas ref={canvasRef} className="game-canvas" />;
        };

        // Main App Component
        const App = () => {
            const { currentUser, login, register, logout, updateBalance } = useUser();
            const { gameState, multiplier, currentBet, hasActiveBet, hasCashedOut, placeBet, cashOut, startNewRound } = useGame();
            const [betAmount, setBetAmount] = useState(10);
            const [showAuth, setShowAuth] = useState(false);
            const [authMode, setAuthMode] = useState('login');

            useEffect(() => {
                if (currentUser) {
                    startNewRound();
                }
            }, [currentUser]);

            const handlePlaceBet = () => {
                if (!currentUser || betAmount <= 0 || betAmount > currentUser.balance) {
                    alert('Invalid bet amount!');
                    return;
                }
                
                if (placeBet(betAmount)) {
                    updateBalance(currentUser.balance - betAmount);
                }
            };

            const handleCashOut = () => {
                if (cashOut()) {
                    const winAmount = currentBet * multiplier;
                    updateBalance(currentUser.balance + winAmount);
                }
            };

            const handleAuth = (username, password, email = '') => {
                if (authMode === 'login') {
                    if (login(username, password)) {
                        setShowAuth(false);
                        alert(`Welcome back, ${username}!`);
                    } else {
                        alert('Invalid credentials!');
                    }
                } else {
                    if (register({ username, password, email })) {
                        setShowAuth(false);
                        alert(`Welcome to RocketBet, ${username}!`);
                    } else {
                        alert('Username already exists!');
                    }
                }
            };

            const getGameStatus = () => {
                switch (gameState) {
                    case 'waiting':
                        return 'Place your bet for the next round!';
                    case 'countdown':
                        return 'Round starting soon...';
                    case 'flying':
                        return 'Rocket is flying! Cash out before it crashes!';
                    case 'crashed':
                        return `Rocket crashed at ${multiplier.toFixed(2)}x!`;
                    default:
                        return 'Waiting...';
                }
            };

            return (
                <div className="app">
                    <header className="header">
                        <div className="container">
                            <div className="logo">
                                <i className="fas fa-rocket"></i>
                                <span>RocketBet React</span>
                            </div>
                            
                            {currentUser ? (
                                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                                    <span style={{ color: '#00b894', fontFamily: 'Orbitron', fontWeight: 'bold' }}>
                                        Balance: ${currentUser.balance.toFixed(2)}
                                    </span>
                                    <button 
                                        onClick={logout}
                                        style={{
                                            background: 'transparent',
                                            border: '2px solid #ff6b6b',
                                            color: '#ff6b6b',
                                            padding: '0.5rem 1rem',
                                            borderRadius: '8px',
                                            cursor: 'pointer'
                                        }}
                                    >
                                        Logout
                                    </button>
                                </div>
                            ) : (
                                <div style={{ display: 'flex', gap: '1rem' }}>
                                    <button 
                                        onClick={() => { setAuthMode('login'); setShowAuth(true); }}
                                        className="btn"
                                    >
                                        Login
                                    </button>
                                    <button 
                                        onClick={() => { setAuthMode('register'); setShowAuth(true); }}
                                        className="btn"
                                    >
                                        Register
                                    </button>
                                </div>
                            )}
                        </div>
                    </header>

                    <main className="main-content">
                        {!currentUser ? (
                            <section className="hero">
                                <div className="container">
                                    <h1>Welcome to <span className="highlight">RocketBet React</span></h1>
                                    <p>Experience the next-generation Rocket Game with enhanced graphics and smooth React animations!</p>
                                    <button 
                                        onClick={() => { setAuthMode('register'); setShowAuth(true); }}
                                        className="btn"
                                    >
                                        <i className="fas fa-rocket"></i>
                                        Start Playing
                                    </button>
                                    <div className="rocket-preview">🚀</div>
                                </div>
                            </section>
                        ) : (
                            <section className="rocket-game">
                                <div className="container">
                                    <h2 className="game-title">
                                        <i className="fas fa-rocket"></i>
                                        Enhanced Rocket Game
                                    </h2>
                                    
                                    <div className="game-canvas-container">
                                        <GameCanvas />
                                        {gameState === 'flying' && (
                                            <div className="multiplier-overlay">
                                                {multiplier.toFixed(2)}x
                                            </div>
                                        )}
                                        <div className="game-status">
                                            {getGameStatus()}
                                        </div>
                                    </div>
                                    
                                    <div className="game-controls">
                                        <div className="control-section">
                                            <h3>Place Bet</h3>
                                            <input
                                                type="number"
                                                className="bet-input"
                                                value={betAmount}
                                                onChange={(e) => setBetAmount(Number(e.target.value))}
                                                min="1"
                                                max={currentUser?.balance || 1000}
                                                disabled={gameState !== 'waiting'}
                                            />
                                            <button
                                                className="control-btn btn-place-bet"
                                                onClick={handlePlaceBet}
                                                disabled={gameState !== 'waiting'}
                                            >
                                                Place Bet ${betAmount}
                                            </button>
                                        </div>
                                        
                                        <div className="control-section">
                                            <h3>Cash Out</h3>
                                            <div style={{ marginBottom: '1rem', fontSize: '1.2rem', fontFamily: 'Orbitron' }}>
                                                Potential Win: ${(currentBet * multiplier).toFixed(2)}
                                            </div>
                                            <button
                                                className="control-btn btn-cash-out"
                                                onClick={handleCashOut}
                                                disabled={!hasActiveBet || hasCashedOut || gameState !== 'flying'}
                                            >
                                                Cash Out {multiplier.toFixed(2)}x
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        )}
                    </main>

                    {showAuth && (
                        <div style={{
                            position: 'fixed',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: 'rgba(0, 0, 0, 0.8)',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            zIndex: 2000
                        }}>
                            <div style={{
                                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
                                padding: '2rem',
                                borderRadius: '15px',
                                border: '2px solid var(--border-color)',
                                minWidth: '400px'
                            }}>
                                <h2 style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                                    {authMode === 'login' ? 'Login' : 'Register'}
                                </h2>
                                <form onSubmit={(e) => {
                                    e.preventDefault();
                                    const formData = new FormData(e.target);
                                    handleAuth(
                                        formData.get('username'),
                                        formData.get('password'),
                                        formData.get('email')
                                    );
                                }}>
                                    <input
                                        name="username"
                                        placeholder="Username"
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '12px',
                                            margin: '0.5rem 0',
                                            border: '2px solid var(--border-color)',
                                            borderRadius: '8px',
                                            background: 'rgba(0, 0, 0, 0.5)',
                                            color: 'white'
                                        }}
                                    />
                                    {authMode === 'register' && (
                                        <input
                                            name="email"
                                            type="email"
                                            placeholder="Email"
                                            required
                                            style={{
                                                width: '100%',
                                                padding: '12px',
                                                margin: '0.5rem 0',
                                                border: '2px solid var(--border-color)',
                                                borderRadius: '8px',
                                                background: 'rgba(0, 0, 0, 0.5)',
                                                color: 'white'
                                            }}
                                        />
                                    )}
                                    <input
                                        name="password"
                                        type="password"
                                        placeholder="Password"
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '12px',
                                            margin: '0.5rem 0',
                                            border: '2px solid var(--border-color)',
                                            borderRadius: '8px',
                                            background: 'rgba(0, 0, 0, 0.5)',
                                            color: 'white'
                                        }}
                                    />
                                    <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
                                        <button type="submit" className="btn" style={{ flex: 1 }}>
                                            {authMode === 'login' ? 'Login' : 'Register'}
                                        </button>
                                        <button 
                                            type="button" 
                                            onClick={() => setShowAuth(false)}
                                            style={{
                                                flex: 1,
                                                background: 'transparent',
                                                border: '2px solid #666',
                                                color: '#666',
                                                padding: '12px',
                                                borderRadius: '8px',
                                                cursor: 'pointer'
                                            }}
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </form>
                                <p style={{ textAlign: 'center', marginTop: '1rem' }}>
                                    {authMode === 'login' ? "Don't have an account? " : "Already have an account? "}
                                    <button
                                        onClick={() => setAuthMode(authMode === 'login' ? 'register' : 'login')}
                                        style={{
                                            background: 'none',
                                            border: 'none',
                                            color: 'var(--primary-color)',
                                            cursor: 'pointer',
                                            textDecoration: 'underline'
                                        }}
                                    >
                                        {authMode === 'login' ? 'Register here' : 'Login here'}
                                    </button>
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // Render the app
        const AppWithProviders = () => (
            <UserProvider>
                <GameProvider>
                    <App />
                </GameProvider>
            </UserProvider>
        );

        ReactDOM.render(<AppWithProviders />, document.getElementById('root'));
    </script>
</body>
</html>
