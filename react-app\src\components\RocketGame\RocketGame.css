.rocket-game-container {
  min-height: 100vh;
  padding: 4rem 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(26, 26, 46, 0.9) 100%);
}

.game-title {
  text-align: center;
  margin-bottom: 3rem;
  font-size: clamp(2rem, 5vw, 3.5rem);
  background: linear-gradient(45deg, var(--primary-color), #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.3));
}

.game-title i {
  margin-right: 1rem;
  color: var(--primary-color);
  filter: drop-shadow(0 0 15px var(--glow-color));
}

.game-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.game-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.game-canvas-container {
  position: relative;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  border: 2px solid var(--border-color);
  overflow: hidden;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.5),
    inset 0 0 20px rgba(255, 107, 107, 0.1);
}

.game-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Login Prompt Styles */
.login-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.prompt-content {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 3rem;
  max-width: 500px;
}

.rocket-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 0 20px var(--glow-color));
  animation: float 3s ease-in-out infinite;
}

.prompt-content h2 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.prompt-content p {
  margin-bottom: 2rem;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.prompt-features {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.feature i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.feature span {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .game-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .game-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .rocket-game-container {
    padding: 2rem 0;
  }
  
  .game-title {
    margin-bottom: 2rem;
  }
  
  .prompt-content {
    padding: 2rem;
    margin: 0 1rem;
  }
  
  .prompt-features {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .feature {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .game-layout {
    gap: 1rem;
  }
  
  .prompt-content {
    padding: 1.5rem;
  }
  
  .rocket-icon {
    font-size: 3rem;
  }
}
