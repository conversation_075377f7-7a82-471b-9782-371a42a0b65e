{"name": "rocketbet-react", "version": "2.0.0", "description": "Modern React-based gambling site with enhanced graphics", "main": "src/index.js", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "dev": "webpack serve --mode development --open"}, "keywords": ["react", "gambling", "rocket-game", "casino", "betting", "graphics"], "author": "RocketBet Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "three": "^0.155.0", "@react-three/fiber": "^8.13.0", "@react-three/drei": "^9.80.0", "framer-motion": "^10.16.0", "styled-components": "^6.0.0", "react-spring": "^9.7.0", "zustand": "^4.4.0", "react-use": "^17.4.0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}}