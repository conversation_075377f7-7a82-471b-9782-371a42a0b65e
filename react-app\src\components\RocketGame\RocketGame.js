import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useUser } from '../../contexts/UserContext';
import { useGame } from '../../contexts/GameContext';
import GameCanvas from './GameCanvas';
import GameControls from './GameControls';
import GameInfo from './GameInfo';
import MultiplierDisplay from './MultiplierDisplay';
import './RocketGame.css';

const RocketGame = () => {
  const { currentUser } = useUser();
  const { gameState, startNewRound } = useGame();
  const gameInitialized = useRef(false);

  useEffect(() => {
    // Initialize game when user is logged in and game hasn't started
    if (currentUser && !gameInitialized.current) {
      gameInitialized.current = true;
      setTimeout(() => {
        startNewRound();
      }, 1000);
    }
  }, [currentUser, startNewRound]);

  if (!currentUser) {
    return (
      <div className="rocket-game-container">
        <div className="container">
          <motion.div 
            className="login-prompt"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="prompt-content">
              <i className="fas fa-rocket rocket-icon"></i>
              <h2>Ready to Launch?</h2>
              <p>Please log in or register to start playing the Rocket Game</p>
              <div className="prompt-features">
                <div className="feature">
                  <i className="fas fa-chart-line"></i>
                  <span>Real-time multipliers</span>
                </div>
                <div className="feature">
                  <i className="fas fa-coins"></i>
                  <span>Instant payouts</span>
                </div>
                <div className="feature">
                  <i className="fas fa-shield-alt"></i>
                  <span>Secure gaming</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="rocket-game-container">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="game-title">
            <i className="fas fa-rocket"></i>
            Rocket Game
          </h2>
          
          <div className="game-layout">
            <div className="game-main">
              <div className="game-canvas-container">
                <GameCanvas />
                <MultiplierDisplay />
              </div>
              <GameControls />
            </div>
            
            <div className="game-sidebar">
              <GameInfo />
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default RocketGame;
