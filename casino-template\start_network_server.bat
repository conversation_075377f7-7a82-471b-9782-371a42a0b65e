@echo off
echo 🎰 Casino Template Network Server
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ⚠️  Not running as Administrator
    echo    This might cause firewall issues
    echo.
)

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo 🔍 Running network diagnostics...
python network_test.py

echo.
echo 🚀 Starting network server...
echo 📱 This server will be accessible from other devices on your network
echo.

python server_network.py

pause
