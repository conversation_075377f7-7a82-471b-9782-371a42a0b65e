<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RocketBet - Premium Gambling Experience</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Age Verification Modal -->
    <div id="ageVerificationModal" class="modal">
        <div class="modal-content">
            <h2><i class="fas fa-exclamation-triangle"></i> Age Verification Required</h2>
            <p>You must be 18 years or older to access this gambling site.</p>
            <p>Please confirm your age to continue.</p>
            <div class="age-buttons">
                <button id="confirmAge" class="btn btn-primary">I am 18+ years old</button>
                <button id="denyAge" class="btn btn-secondary">I am under 18</button>
            </div>
            <div class="responsible-gambling">
                <p><strong>Gamble Responsibly</strong></p>
                <p>Gambling can be addictive. Please play responsibly and within your means.</p>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-rocket"></i>
                <span>RocketBet</span>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="#home" class="nav-link active">Home</a></li>
                    <li><a href="#game" class="nav-link">Rocket Game</a></li>
                    <li><a href="#responsible" class="nav-link">Responsible Gaming</a></li>
                </ul>
            </nav>
            <div class="user-section">
                <div id="userInfo" class="user-info" style="display: none;">
                    <span class="balance">Balance: $<span id="userBalance">1000.00</span></span>
                    <button id="logoutBtn" class="btn btn-outline">Logout</button>
                </div>
                <div id="authButtons" class="auth-buttons">
                    <button id="loginBtn" class="btn btn-outline">Login</button>
                    <button id="registerBtn" class="btn btn-primary">Register</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>Welcome to <span class="highlight">RocketBet</span></h1>
                    <p>Experience the thrill of the Rocket Game - Watch it soar, cash out before it crashes!</p>
                    <button id="playNowBtn" class="btn btn-large btn-primary">
                        <i class="fas fa-rocket"></i> Play Rocket Game
                    </button>
                </div>
                <div class="hero-animation">
                    <div class="rocket-preview">
                        <i class="fas fa-rocket"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Game Section -->
        <section id="game" class="game-section">
            <div class="container">
                <h2>Rocket Game</h2>
                <div class="game-container">
                    <div class="game-area">
                        <div class="game-display">
                            <canvas id="gameCanvas" width="800" height="400"></canvas>
                            <div class="multiplier-display">
                                <span id="currentMultiplier">1.00x</span>
                            </div>
                            <div class="game-status">
                                <span id="gameStatus">Waiting for next round...</span>
                            </div>
                        </div>
                        <div class="game-controls">
                            <div class="bet-section">
                                <label for="betAmount">Bet Amount ($)</label>
                                <input type="number" id="betAmount" min="1" max="1000" value="10" step="1">
                                <button id="placeBetBtn" class="btn btn-primary">Place Bet</button>
                            </div>
                            <div class="cash-out-section">
                                <button id="cashOutBtn" class="btn btn-success" disabled>Cash Out</button>
                                <div class="potential-win">
                                    Potential Win: $<span id="potentialWin">0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-history">
                            <h3>Recent Results</h3>
                            <div id="gameHistory" class="history-list">
                                <!-- History items will be added here -->
                            </div>
                        </div>
                        <div class="game-stats">
                            <h3>Your Stats</h3>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-label">Games Played</span>
                                    <span class="stat-value" id="gamesPlayed">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Total Wagered</span>
                                    <span class="stat-value">$<span id="totalWagered">0.00</span></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Total Won</span>
                                    <span class="stat-value">$<span id="totalWon">0.00</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Responsible Gaming Section -->
        <section id="responsible" class="responsible-section">
            <div class="container">
                <h2>Responsible Gaming</h2>
                <div class="responsible-content">
                    <div class="warning-box">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Important Notice</h3>
                        <p>This site is for demonstration purposes only. No real money is involved.</p>
                    </div>
                    <div class="guidelines">
                        <h3>Gambling Guidelines</h3>
                        <ul>
                            <li>Only gamble with money you can afford to lose</li>
                            <li>Set time and money limits before you start</li>
                            <li>Never chase your losses</li>
                            <li>Take regular breaks</li>
                            <li>Don't gamble when upset, angry, or depressed</li>
                        </ul>
                    </div>
                    <div class="help-resources">
                        <h3>Need Help?</h3>
                        <p>If you or someone you know has a gambling problem, help is available:</p>
                        <ul>
                            <li>National Problem Gambling Helpline: 1-800-522-4700</li>
                            <li>Gamblers Anonymous: www.gamblersanonymous.org</li>
                            <li>National Council on Problem Gambling: www.ncpgambling.org</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>RocketBet</h4>
                    <p>Premium gambling experience with responsible gaming at its core.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#game">Rocket Game</a></li>
                        <li><a href="#responsible">Responsible Gaming</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">18+ Only</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 RocketBet. For demonstration purposes only. Please gamble responsibly.</p>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Login</h2>
            <form id="loginForm">
                <input type="text" id="loginUsername" placeholder="Username" required>
                <input type="password" id="loginPassword" placeholder="Password" required>
                <button type="submit" class="btn btn-primary">Login</button>
            </form>
            <p>Don't have an account? <a href="#" id="showRegister">Register here</a></p>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Register</h2>
            <form id="registerForm">
                <input type="text" id="registerUsername" placeholder="Username" required>
                <input type="email" id="registerEmail" placeholder="Email" required>
                <input type="password" id="registerPassword" placeholder="Password" required>
                <input type="password" id="confirmPassword" placeholder="Confirm Password" required>
                <div class="checkbox-group">
                    <input type="checkbox" id="ageConfirm" required>
                    <label for="ageConfirm">I confirm that I am 18 years or older</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="termsAccept" required>
                    <label for="termsAccept">I accept the Terms of Service</label>
                </div>
                <button type="submit" class="btn btn-primary">Register</button>
            </form>
            <p>Already have an account? <a href="#" id="showLogin">Login here</a></p>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/rocket-game.js"></script>
</body>
</html>
