#!/bin/bash

echo "🎰 Starting Casino Template Server on Network..."
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python is not installed"
        echo "Please install Python from https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "🚀 Starting Python server on 192.168.2.221..."
echo "📱 Other devices on your network can access the game!"
echo
$PYTHON_CMD server.py 192.168.2.221
