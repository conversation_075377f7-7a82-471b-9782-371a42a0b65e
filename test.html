<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RocketBet - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a2e;
            color: white;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #ff6b6b;
        }
        .test-button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: rgba(0, 184, 148, 0.2);
            border: 1px solid #00b894;
        }
        .error {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <h1>RocketBet - Test Suite</h1>
    
    <div class="test-section">
        <h2>Manual Testing Checklist</h2>
        <p>Please test the following features manually:</p>
        <ul>
            <li>✅ Age verification modal appears on first visit</li>
            <li>✅ User registration with validation</li>
            <li>✅ User login/logout functionality</li>
            <li>✅ Rocket game mechanics (betting, cash out, crash)</li>
            <li>✅ Balance updates correctly</li>
            <li>✅ Game history tracking</li>
            <li>✅ Responsive design on mobile</li>
            <li>✅ Responsible gambling warnings</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Automated Tests</h2>
        <button class="test-button" onclick="testLocalStorage()">Test Local Storage</button>
        <button class="test-button" onclick="testUserValidation()">Test User Validation</button>
        <button class="test-button" onclick="testGameMechanics()">Test Game Mechanics</button>
        <button class="test-button" onclick="testResponsibleGaming()">Test Responsible Gaming</button>
        <div id="testResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Performance Tests</h2>
        <button class="test-button" onclick="testCanvasPerformance()">Test Canvas Performance</button>
        <button class="test-button" onclick="testMemoryUsage()">Test Memory Usage</button>
        <div id="performanceResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Quick Access</h2>
        <a href="index.html" class="test-button" style="text-decoration: none; display: inline-block;">Go to Main Site</a>
    </div>

    <script>
        function addResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function addPerformanceResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('performanceResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function testLocalStorage() {
            try {
                // Test localStorage functionality
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('testKey', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('testKey'));
                
                if (retrieved.test === 'data') {
                    addResult('✅ Local Storage: Working correctly');
                } else {
                    addResult('❌ Local Storage: Data retrieval failed', false);
                }
                
                localStorage.removeItem('testKey');
            } catch (error) {
                addResult(`❌ Local Storage: Error - ${error.message}`, false);
            }
        }
        
        function testUserValidation() {
            // Test user validation functions
            const testCases = [
                { username: 'test', email: '<EMAIL>', password: 'password123', valid: true },
                { username: '', email: '<EMAIL>', password: 'password123', valid: false },
                { username: 'test', email: 'invalid-email', password: 'password123', valid: false },
                { username: 'test', email: '<EMAIL>', password: '123', valid: false }
            ];
            
            let passed = 0;
            testCases.forEach((testCase, index) => {
                const isValid = testCase.username && 
                               testCase.email.includes('@') && 
                               testCase.password.length >= 6;
                
                if (isValid === testCase.valid) {
                    passed++;
                }
            });
            
            if (passed === testCases.length) {
                addResult('✅ User Validation: All test cases passed');
            } else {
                addResult(`❌ User Validation: ${passed}/${testCases.length} test cases passed`, false);
            }
        }
        
        function testGameMechanics() {
            // Test basic game mechanics
            try {
                // Test multiplier calculation
                const testMultiplier = Math.pow(1.1, 2 * 2); // 2 seconds elapsed
                if (testMultiplier > 1.0 && testMultiplier < 10.0) {
                    addResult('✅ Game Mechanics: Multiplier calculation working');
                } else {
                    addResult('❌ Game Mechanics: Multiplier calculation failed', false);
                }
                
                // Test crash point generation
                let validCrashPoints = 0;
                for (let i = 0; i < 100; i++) {
                    const random = Math.random();
                    const crashPoint = Math.max(1.01, Math.min(50.0, 1 / (1 - random * 0.99)));
                    if (crashPoint >= 1.01 && crashPoint <= 50.0) {
                        validCrashPoints++;
                    }
                }
                
                if (validCrashPoints === 100) {
                    addResult('✅ Game Mechanics: Crash point generation working');
                } else {
                    addResult(`❌ Game Mechanics: ${validCrashPoints}/100 valid crash points`, false);
                }
                
            } catch (error) {
                addResult(`❌ Game Mechanics: Error - ${error.message}`, false);
            }
        }
        
        function testResponsibleGaming() {
            try {
                // Test daily limit tracking
                const today = new Date().toDateString();
                const testBets = {};
                testBets[today] = 100;
                
                localStorage.setItem('dailyBets', JSON.stringify(testBets));
                const retrieved = JSON.parse(localStorage.getItem('dailyBets'));
                
                if (retrieved[today] === 100) {
                    addResult('✅ Responsible Gaming: Daily limit tracking working');
                } else {
                    addResult('❌ Responsible Gaming: Daily limit tracking failed', false);
                }
                
                localStorage.removeItem('dailyBets');
            } catch (error) {
                addResult(`❌ Responsible Gaming: Error - ${error.message}`, false);
            }
        }
        
        function testCanvasPerformance() {
            try {
                const canvas = document.createElement('canvas');
                canvas.width = 800;
                canvas.height = 400;
                const ctx = canvas.getContext('2d');
                
                const startTime = performance.now();
                
                // Simulate drawing operations
                for (let i = 0; i < 1000; i++) {
                    ctx.fillStyle = `rgb(${i % 255}, 100, 150)`;
                    ctx.fillRect(i % 800, i % 400, 10, 10);
                }
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (duration < 100) {
                    addPerformanceResult(`✅ Canvas Performance: ${duration.toFixed(2)}ms (Excellent)`);
                } else if (duration < 500) {
                    addPerformanceResult(`✅ Canvas Performance: ${duration.toFixed(2)}ms (Good)`);
                } else {
                    addPerformanceResult(`⚠️ Canvas Performance: ${duration.toFixed(2)}ms (Slow)`, false);
                }
                
            } catch (error) {
                addPerformanceResult(`❌ Canvas Performance: Error - ${error.message}`, false);
            }
        }
        
        function testMemoryUsage() {
            if (performance.memory) {
                const memory = performance.memory;
                const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
                
                addPerformanceResult(`✅ Memory Usage: ${usedMB}MB / ${totalMB}MB`);
                
                if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.9) {
                    addPerformanceResult('⚠️ Memory Warning: High memory usage detected', false);
                }
            } else {
                addPerformanceResult('ℹ️ Memory Usage: Not available in this browser');
            }
        }
        
        // Run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testLocalStorage();
                testUserValidation();
                testGameMechanics();
                testResponsibleGaming();
            }, 1000);
        });
    </script>
</body>
</html>
